/**
 * New Authentication System Frontend Integration
 * Handles communication with the new auth-server.js backend
 */

class NewAuthSystem {
    constructor() {
        this.apiBaseUrl = 'http://localhost:3001/api';
        this.token = localStorage.getItem('authToken');
        this.user = this.getStoredUser();
        
        // Initialize on page load
        this.init();
    }

    /**
     * Initialize the authentication system
     */
    init() {
        console.log('🔐 Initializing New Authentication System');
        
        // Check if user is logged in
        if (this.token) {
            this.verifyToken();
        }

        // Set up form handlers
        this.setupFormHandlers();
        
        // Set up navigation
        this.updateNavigation();
    }

    /**
     * Set up form event handlers
     */
    setupFormHandlers() {
        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Registration form
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // Password change form
        const passwordForm = document.getElementById('passwordForm');
        if (passwordForm) {
            passwordForm.addEventListener('submit', (e) => this.handlePasswordChange(e));
        }

        // Profile update form
        const profileForm = document.getElementById('profileForm');
        if (profileForm) {
            profileForm.addEventListener('submit', (e) => this.handleProfileUpdate(e));
        }
    }

    /**
     * Make authenticated API request
     */
    async makeRequest(endpoint, options = {}) {
        const url = `${this.apiBaseUrl}${endpoint}`;
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        // Add authorization header if token exists
        if (this.token) {
            defaultOptions.headers['Authorization'] = `Bearer ${this.token}`;
        }

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers,
            },
        };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error?.message || 'Request failed');
            }

            return data;

        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    /**
     * Handle user registration
     */
    async handleRegister(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        
        const userData = {
            email: formData.get('email'),
            password: formData.get('password'),
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            phone: formData.get('phone')
        };

        try {
            this.setFormLoading(form, true);
            
            const result = await this.makeRequest('/auth/register', {
                method: 'POST',
                body: JSON.stringify(userData)
            });

            this.showMessage('Registration successful! Please check your email to verify your account.', 'success');
            
            // In development, auto-verify for testing
            if (result.verificationToken) {
                console.log('🔧 Development mode - auto-verifying email');
                await this.verifyEmail(result.verificationToken);
            }

        } catch (error) {
            this.showMessage(error.message, 'error');
        } finally {
            this.setFormLoading(form, false);
        }
    }

    /**
     * Handle user login
     */
    async handleLogin(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        
        const loginData = {
            email: formData.get('email'),
            password: formData.get('password'),
            rememberMe: formData.get('rememberMe') === 'on'
        };

        try {
            this.setFormLoading(form, true);
            
            const result = await this.makeRequest('/auth/login', {
                method: 'POST',
                body: JSON.stringify(loginData)
            });

            // Store authentication data
            this.setAuthData(result.token, result.user);
            
            this.showMessage('Login successful!', 'success');
            
            // Redirect to account page or return URL
            const returnUrl = new URLSearchParams(window.location.search).get('returnUrl') || '/public/account.html';
            setTimeout(() => {
                window.location.href = returnUrl;
            }, 1000);

        } catch (error) {
            this.showMessage(error.message, 'error');
        } finally {
            this.setFormLoading(form, false);
        }
    }

    /**
     * Handle password change
     */
    async handlePasswordChange(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        
        const passwordData = {
            currentPassword: formData.get('currentPassword'),
            newPassword: formData.get('newPassword')
        };

        // Confirm password validation
        const confirmPassword = formData.get('confirmPassword');
        if (passwordData.newPassword !== confirmPassword) {
            this.showMessage('New passwords do not match', 'error');
            return;
        }

        try {
            this.setFormLoading(form, true);
            
            await this.makeRequest('/user/password', {
                method: 'PUT',
                body: JSON.stringify(passwordData)
            });

            this.showMessage('Password changed successfully! Please log in again.', 'success');
            
            // Logout and redirect to login
            setTimeout(() => {
                this.logout();
            }, 2000);

        } catch (error) {
            this.showMessage(error.message, 'error');
        } finally {
            this.setFormLoading(form, false);
        }
    }

    /**
     * Handle profile update
     */
    async handleProfileUpdate(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        
        const profileData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            phone: formData.get('phone'),
            dateOfBirth: formData.get('dateOfBirth'),
            bio: formData.get('bio'),
            website: formData.get('website'),
            location: formData.get('location')
        };

        try {
            this.setFormLoading(form, true);
            
            await this.makeRequest('/user/info', {
                method: 'PUT',
                body: JSON.stringify(profileData)
            });

            this.showMessage('Profile updated successfully!', 'success');
            
            // Refresh user data
            await this.loadUserProfile();

        } catch (error) {
            this.showMessage(error.message, 'error');
        } finally {
            this.setFormLoading(form, false);
        }
    }

    /**
     * Verify JWT token
     */
    async verifyToken() {
        try {
            const result = await this.makeRequest('/auth/verify');
            this.user = result.user;
            this.updateNavigation();
            return true;
        } catch (error) {
            console.log('Token verification failed:', error.message);
            this.clearAuthData();
            return false;
        }
    }

    /**
     * Verify email with token
     */
    async verifyEmail(token) {
        try {
            await this.makeRequest('/auth/verify-email', {
                method: 'POST',
                body: JSON.stringify({ token })
            });
            
            this.showMessage('Email verified successfully!', 'success');
            return true;
        } catch (error) {
            this.showMessage(error.message, 'error');
            return false;
        }
    }

    /**
     * Request password reset
     */
    async requestPasswordReset(email) {
        try {
            const result = await this.makeRequest('/auth/forgot-password', {
                method: 'POST',
                body: JSON.stringify({ email })
            });
            
            this.showMessage(result.message, 'success');
            
            // In development, show reset token
            if (result.resetToken) {
                console.log('🔧 Development mode - reset token:', result.resetToken);
            }
            
            return true;
        } catch (error) {
            this.showMessage(error.message, 'error');
            return false;
        }
    }

    /**
     * Reset password with token
     */
    async resetPassword(token, newPassword) {
        try {
            await this.makeRequest('/auth/reset-password', {
                method: 'POST',
                body: JSON.stringify({ token, newPassword })
            });
            
            this.showMessage('Password reset successfully! Please log in.', 'success');
            return true;
        } catch (error) {
            this.showMessage(error.message, 'error');
            return false;
        }
    }

    /**
     * Load user profile data
     */
    async loadUserProfile() {
        try {
            const result = await this.makeRequest('/user/profile');
            this.user = result.user;
            this.populateProfileForm();
            return result.user;
        } catch (error) {
            console.error('Error loading profile:', error);
            throw error;
        }
    }

    /**
     * Logout user
     */
    async logout() {
        try {
            if (this.token) {
                await this.makeRequest('/auth/logout', { method: 'POST' });
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            this.clearAuthData();
            window.location.href = '/index.html';
        }
    }

    /**
     * Set authentication data
     */
    setAuthData(token, user) {
        this.token = token;
        this.user = user;
        localStorage.setItem('authToken', token);
        localStorage.setItem('currentUser', JSON.stringify(user));
        this.updateNavigation();
    }

    /**
     * Clear authentication data
     */
    clearAuthData() {
        this.token = null;
        this.user = null;
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        this.updateNavigation();
    }

    /**
     * Get stored user data
     */
    getStoredUser() {
        try {
            const userData = localStorage.getItem('currentUser');
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.error('Error parsing stored user data:', error);
            return null;
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!(this.token && this.user);
    }

    /**
     * Update navigation based on auth status
     */
    updateNavigation() {
        const loginLinks = document.querySelectorAll('.login-link');
        const logoutLinks = document.querySelectorAll('.logout-link');
        const userInfo = document.querySelectorAll('.user-info');

        if (this.isAuthenticated()) {
            // Hide login links, show logout links
            loginLinks.forEach(link => link.style.display = 'none');
            logoutLinks.forEach(link => link.style.display = 'block');

            // Update user info
            userInfo.forEach(element => {
                element.textContent = this.user.firstName
                    ? `${this.user.firstName} ${this.user.lastName}`
                    : this.user.email;
                element.style.display = 'block';
            });
        } else {
            // Show login links, hide logout links
            loginLinks.forEach(link => link.style.display = 'block');
            logoutLinks.forEach(link => link.style.display = 'none');
            userInfo.forEach(element => element.style.display = 'none');
        }
    }

    /**
     * Populate profile form with user data
     */
    populateProfileForm() {
        if (!this.user) return;

        const fields = [
            'firstName', 'lastName', 'email', 'phone', 'dateOfBirth',
            'bio', 'website', 'location', 'twitterHandle', 'linkedinUrl', 'githubUsername'
        ];

        fields.forEach(field => {
            const element = document.getElementById(field);
            if (element && this.user[field]) {
                element.value = this.user[field];
            }
        });

        // Handle checkboxes
        const checkboxes = ['emailNotifications', 'smsNotifications', 'marketingEmails'];
        checkboxes.forEach(field => {
            const element = document.getElementById(field);
            if (element && this.user[field] !== undefined) {
                element.checked = this.user[field];
            }
        });
    }

    /**
     * Set form loading state
     */
    setFormLoading(form, loading) {
        const submitButton = form.querySelector('button[type="submit"]');
        const inputs = form.querySelectorAll('input, textarea, select');

        if (loading) {
            submitButton.disabled = true;
            submitButton.textContent = 'Loading...';
            inputs.forEach(input => input.disabled = true);
        } else {
            submitButton.disabled = false;
            submitButton.textContent = submitButton.dataset.originalText || 'Submit';
            inputs.forEach(input => input.disabled = false);
        }
    }

    /**
     * Show message to user
     */
    showMessage(message, type = 'info') {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.auth-message');
        existingMessages.forEach(msg => msg.remove());

        // Create new message element
        const messageElement = document.createElement('div');
        messageElement.className = `auth-message alert alert-${type}`;
        messageElement.textContent = message;

        // Style the message
        messageElement.style.cssText = `
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: 4px;
            font-weight: 500;
            ${type === 'success' ? 'background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;' : ''}
            ${type === 'error' ? 'background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;' : ''}
            ${type === 'info' ? 'background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb;' : ''}
        `;

        // Insert message at the top of the form or page
        const form = document.querySelector('form');
        if (form) {
            form.insertBefore(messageElement, form.firstChild);
        } else {
            document.body.insertBefore(messageElement, document.body.firstChild);
        }

        // Auto-remove after 5 seconds
        setTimeout(() => {
            messageElement.remove();
        }, 5000);
    }

    /**
     * Protect page (redirect to login if not authenticated)
     */
    protectPage() {
        if (!this.isAuthenticated()) {
            const currentUrl = encodeURIComponent(window.location.pathname + window.location.search);
            window.location.href = `/public/login.html?returnUrl=${currentUrl}`;
            return false;
        }
        return true;
    }

    /**
     * Get user sessions
     */
    async getUserSessions() {
        try {
            const result = await this.makeRequest('/user/sessions');
            return result.sessions;
        } catch (error) {
            console.error('Error getting sessions:', error);
            throw error;
        }
    }

    /**
     * Revoke session
     */
    async revokeSession(sessionId) {
        try {
            await this.makeRequest(`/user/sessions/${sessionId}`, {
                method: 'DELETE'
            });
            this.showMessage('Session revoked successfully', 'success');
            return true;
        } catch (error) {
            this.showMessage(error.message, 'error');
            return false;
        }
    }

    /**
     * Get user activity
     */
    async getUserActivity(limit = 50) {
        try {
            const result = await this.makeRequest(`/user/activity?limit=${limit}`);
            return result.activities;
        } catch (error) {
            console.error('Error getting activity:', error);
            throw error;
        }
    }

    /**
     * Get user statistics
     */
    async getUserStats() {
        try {
            const result = await this.makeRequest('/user/stats');
            return result.stats;
        } catch (error) {
            console.error('Error getting stats:', error);
            throw error;
        }
    }
}

// Initialize the authentication system
window.newAuth = new NewAuthSystem();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NewAuthSystem;
}
