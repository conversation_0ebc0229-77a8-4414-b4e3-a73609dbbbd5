/**
 * Test Script for New Authentication System
 * Run this to test all authentication features
 */

const { db } = require('./database/database');
const authService = require('./services/authService');
const userService = require('./services/userService');

class AuthSystemTester {
    constructor() {
        this.testResults = [];
        this.testUser = null;
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🧪 Starting Authentication System Tests...\n');

        try {
            // Clean up any existing database
            const fs = require('fs');
            const dbPath = './database/auth_system.db';
            if (fs.existsSync(dbPath)) {
                fs.unlinkSync(dbPath);
                console.log('🗑️ Cleaned up existing database');
            }

            // Initialize database
            await this.test('Database Initialization', () => db.initialize());

            // Test user registration
            await this.test('User Registration', () => this.testRegistration());

            // Test email verification
            await this.test('Email Verification', () => this.testEmailVerification());

            // Test user login
            await this.test('User Login', () => this.testLogin());

            // Test token verification
            await this.test('Token Verification', () => this.testTokenVerification());

            // Test profile management
            await this.test('Profile Management', () => this.testProfileManagement());

            // Test password change
            await this.test('Password Change', () => this.testPasswordChange());

            // Test address management
            await this.test('Address Management', () => this.testAddressManagement());

            // Test session management
            await this.test('Session Management', () => this.testSessionManagement());

            // Test password reset
            await this.test('Password Reset', () => this.testPasswordReset());

            // Test security features
            await this.test('Security Features', () => this.testSecurityFeatures());

            // Test cleanup
            await this.test('Database Cleanup', () => this.testCleanup());

            // Print results
            this.printResults();

        } catch (error) {
            console.error('❌ Test suite failed:', error);
        } finally {
            await db.close();
        }
    }

    /**
     * Test user registration
     */
    async testRegistration() {
        const userData = {
            email: '<EMAIL>',
            password: 'TestPassword123!',
            firstName: 'Test',
            lastName: 'User',
            phone: '+1234567890'
        };

        const result = await authService.register(userData);
        
        if (!result.success || !result.userId) {
            throw new Error('Registration failed');
        }

        this.testUser = { ...userData, id: result.userId };
        this.verificationToken = result.verificationToken;
        
        console.log('✅ User registered successfully');
    }

    /**
     * Test email verification
     */
    async testEmailVerification() {
        if (!this.verificationToken) {
            throw new Error('No verification token available');
        }

        const result = await authService.verifyEmail(this.verificationToken);
        
        if (!result.success) {
            throw new Error('Email verification failed');
        }

        console.log('✅ Email verified successfully');
    }

    /**
     * Test user login
     */
    async testLogin() {
        const result = await authService.login(this.testUser.email, this.testUser.password, {
            rememberMe: false,
            ipAddress: '127.0.0.1',
            userAgent: 'Test Agent'
        });

        if (!result.success || !result.token) {
            throw new Error('Login failed');
        }

        this.authToken = result.token;
        console.log('✅ User logged in successfully');
    }

    /**
     * Test token verification
     */
    async testTokenVerification() {
        const result = await authService.verifyToken(this.authToken);
        
        if (!result.valid) {
            throw new Error('Token verification failed');
        }

        console.log('✅ Token verified successfully');
    }

    /**
     * Test profile management
     */
    async testProfileManagement() {
        // Get profile
        const profile = await userService.getUserProfile(this.testUser.id);
        if (!profile || profile.email !== this.testUser.email) {
            throw new Error('Failed to get user profile');
        }

        // Update user info
        await userService.updateUserInfo(this.testUser.id, {
            firstName: 'Updated',
            lastName: 'Name'
        });

        // Update profile
        await userService.updateUserProfile(this.testUser.id, {
            bio: 'Test user bio',
            website: 'https://example.com',
            location: 'Test City'
        });

        // Update preferences
        await userService.updateNotificationPreferences(this.testUser.id, {
            emailNotifications: false,
            smsNotifications: true,
            marketingEmails: false
        });

        console.log('✅ Profile management working');
    }

    /**
     * Test password change
     */
    async testPasswordChange() {
        const newPassword = 'NewTestPassword123!';
        
        await userService.changePassword(this.testUser.id, this.testUser.password, newPassword);
        
        // Test login with new password
        const loginResult = await authService.login(this.testUser.email, newPassword, {
            ipAddress: '127.0.0.1',
            userAgent: 'Test Agent'
        });

        if (!loginResult.success) {
            throw new Error('Login with new password failed');
        }

        this.testUser.password = newPassword;
        this.authToken = loginResult.token;
        
        console.log('✅ Password change working');
    }

    /**
     * Test address management
     */
    async testAddressManagement() {
        // Add address
        const addressData = {
            type: 'home',
            label: 'Home Address',
            streetAddress: '123 Test Street',
            city: 'Test City',
            stateProvince: 'Test State',
            postalCode: '12345',
            country: 'US',
            isDefault: true
        };

        const addResult = await userService.addAddress(this.testUser.id, addressData);
        if (!addResult.success) {
            throw new Error('Failed to add address');
        }

        const addressId = addResult.addressId;

        // Update address
        await userService.updateAddress(this.testUser.id, addressId, {
            streetAddress: '456 Updated Street'
        });

        // Delete address
        await userService.deleteAddress(this.testUser.id, addressId);

        console.log('✅ Address management working');
    }

    /**
     * Test session management
     */
    async testSessionManagement() {
        // Get sessions
        const sessions = await authService.getUserSessions(this.testUser.id);
        if (!Array.isArray(sessions) || sessions.length === 0) {
            throw new Error('Failed to get user sessions');
        }

        // Test session exists
        const currentSession = sessions.find(s => s.is_active);
        if (!currentSession) {
            throw new Error('No active session found');
        }

        console.log('✅ Session management working');
    }

    /**
     * Test password reset
     */
    async testPasswordReset() {
        // Request password reset
        const resetResult = await authService.generatePasswordResetToken(
            this.testUser.email, 
            '127.0.0.1', 
            'Test Agent'
        );

        if (!resetResult.success || !resetResult.token) {
            throw new Error('Failed to generate password reset token');
        }

        // Reset password
        const newPassword = 'ResetPassword123!';
        await authService.resetPassword(resetResult.token, newPassword);

        // Test login with reset password
        const loginResult = await authService.login(this.testUser.email, newPassword, {
            ipAddress: '127.0.0.1',
            userAgent: 'Test Agent'
        });

        if (!loginResult.success) {
            throw new Error('Login with reset password failed');
        }

        this.testUser.password = newPassword;
        this.authToken = loginResult.token;

        console.log('✅ Password reset working');
    }

    /**
     * Test security features
     */
    async testSecurityFeatures() {
        // Test failed login attempts
        try {
            await authService.login(this.testUser.email, 'wrongpassword', {
                ipAddress: '127.0.0.1',
                userAgent: 'Test Agent'
            });
            throw new Error('Login should have failed with wrong password');
        } catch (error) {
            if (!error.message.includes('Invalid email or password')) {
                throw error;
            }
        }

        // Test user stats
        const stats = await userService.getUserStats(this.testUser.id);
        if (!stats || typeof stats.totalLogins !== 'number') {
            throw new Error('Failed to get user stats');
        }

        // Test activity log
        const activities = await userService.getUserActivity(this.testUser.id, 10);
        if (!Array.isArray(activities)) {
            throw new Error('Failed to get user activities');
        }

        console.log('✅ Security features working');
    }

    /**
     * Test database cleanup
     */
    async testCleanup() {
        await db.cleanup();
        
        const stats = await db.getStats();
        if (!stats || typeof stats.users !== 'number') {
            throw new Error('Failed to get database stats');
        }

        console.log('✅ Database cleanup working');
    }

    /**
     * Run a single test
     */
    async test(name, testFunction) {
        try {
            console.log(`🔍 Testing: ${name}`);
            await testFunction();
            this.testResults.push({ name, status: 'PASS' });
        } catch (error) {
            console.error(`❌ ${name} failed:`, error.message);
            this.testResults.push({ name, status: 'FAIL', error: error.message });
        }
    }

    /**
     * Print test results
     */
    printResults() {
        console.log('\n📊 Test Results:');
        console.log('================');
        
        let passed = 0;
        let failed = 0;

        this.testResults.forEach(result => {
            const status = result.status === 'PASS' ? '✅' : '❌';
            console.log(`${status} ${result.name}`);
            if (result.error) {
                console.log(`   Error: ${result.error}`);
            }
            
            if (result.status === 'PASS') passed++;
            else failed++;
        });

        console.log('\n📈 Summary:');
        console.log(`   Passed: ${passed}`);
        console.log(`   Failed: ${failed}`);
        console.log(`   Total:  ${this.testResults.length}`);
        
        if (failed === 0) {
            console.log('\n🎉 All tests passed! Authentication system is working correctly.');
        } else {
            console.log('\n⚠️  Some tests failed. Please check the errors above.');
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const tester = new AuthSystemTester();
    tester.runAllTests().catch(console.error);
}

module.exports = AuthSystemTester;
