/**
 * Authentication Server
 * New authentication system built from scratch
 * Handles all authentication, user management, and security features
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

// Import services
const { db } = require('./database/database');
const authService = require('./services/authService');
const userService = require('./services/userService');

const app = express();
const PORT = process.env.PORT || 3001;

// ============================================================================
// MIDDLEWARE SETUP
// ============================================================================

// Security middleware
app.use(helmet({
    contentSecurityPolicy: false, // Disable for development
    crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
    origin: process.env.NODE_ENV === 'production' 
        ? ['https://yourdomain.com'] 
        : ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5500'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// Rate limiting
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // Limit each IP to 10 requests per windowMs
    message: { error: 'Too many authentication attempts, please try again later' },
    standardHeaders: true,
    legacyHeaders: false,
});

const generalLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    standardHeaders: true,
    legacyHeaders: false,
});

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Apply rate limiting
app.use('/api/auth/login', authLimiter);
app.use('/api/auth/register', authLimiter);
app.use('/api/auth/reset-password', authLimiter);
app.use('/api', generalLimiter);

// Static files
app.use(express.static('public'));

// ============================================================================
// AUTHENTICATION MIDDLEWARE
// ============================================================================

/**
 * Middleware to authenticate JWT token
 */
async function authenticateToken(req, res, next) {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return res.status(401).json({ 
                success: false, 
                error: { message: 'Access token required' } 
            });
        }

        const verification = await authService.verifyToken(token);
        
        if (!verification.valid) {
            return res.status(403).json({ 
                success: false, 
                error: { message: verification.error || 'Invalid or expired token' } 
            });
        }

        req.user = verification.user;
        req.token = token;
        next();

    } catch (error) {
        console.error('Token verification error:', error);
        return res.status(403).json({ 
            success: false, 
            error: { message: 'Invalid or expired token' } 
        });
    }
}

/**
 * Get client IP address
 */
function getClientIP(req) {
    return req.headers['x-forwarded-for'] || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           '127.0.0.1';
}

// ============================================================================
// AUTHENTICATION ROUTES
// ============================================================================

/**
 * User Registration
 */
app.post('/api/auth/register', async (req, res) => {
    try {
        const { email, password, firstName, lastName, phone } = req.body;
        const ipAddress = getClientIP(req);
        const userAgent = req.headers['user-agent'];

        const result = await authService.register({
            email,
            password,
            firstName,
            lastName,
            phone
        });

        // In production, send verification email instead of returning token
        res.status(201).json({
            success: true,
            message: result.message,
            // Remove this in production:
            verificationToken: result.verificationToken
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * User Login
 */
app.post('/api/auth/login', async (req, res) => {
    try {
        const { email, password, rememberMe } = req.body;
        const ipAddress = getClientIP(req);
        const userAgent = req.headers['user-agent'];

        const result = await authService.login(email, password, {
            rememberMe,
            ipAddress,
            userAgent
        });

        res.json({
            success: true,
            message: 'Login successful',
            token: result.token,
            user: result.user
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(401).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Verify Token / Get Current User
 */
app.get('/api/auth/verify', authenticateToken, async (req, res) => {
    try {
        res.json({
            success: true,
            user: req.user
        });
    } catch (error) {
        console.error('Token verification error:', error);
        res.status(500).json({
            success: false,
            error: { message: 'Internal server error' }
        });
    }
});

/**
 * User Logout
 */
app.post('/api/auth/logout', authenticateToken, async (req, res) => {
    try {
        await authService.logout(req.token);
        
        res.json({
            success: true,
            message: 'Logged out successfully'
        });

    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            success: false,
            error: { message: 'Internal server error' }
        });
    }
});

/**
 * Email Verification
 */
app.post('/api/auth/verify-email', async (req, res) => {
    try {
        const { token } = req.body;

        if (!token) {
            return res.status(400).json({
                success: false,
                error: { message: 'Verification token is required' }
            });
        }

        const result = await authService.verifyEmail(token);

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Email verification error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Request Password Reset
 */
app.post('/api/auth/forgot-password', async (req, res) => {
    try {
        const { email } = req.body;
        const ipAddress = getClientIP(req);
        const userAgent = req.headers['user-agent'];

        if (!email) {
            return res.status(400).json({
                success: false,
                error: { message: 'Email is required' }
            });
        }

        const result = await authService.generatePasswordResetToken(email, ipAddress, userAgent);

        res.json({
            success: true,
            message: result.message,
            // Remove this in production:
            resetToken: result.token
        });

    } catch (error) {
        console.error('Password reset request error:', error);
        res.status(500).json({
            success: false,
            error: { message: 'Internal server error' }
        });
    }
});

/**
 * Reset Password
 */
app.post('/api/auth/reset-password', async (req, res) => {
    try {
        const { token, newPassword } = req.body;

        if (!token || !newPassword) {
            return res.status(400).json({
                success: false,
                error: { message: 'Reset token and new password are required' }
            });
        }

        const result = await authService.resetPassword(token, newPassword);

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Password reset error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

// ============================================================================
// USER MANAGEMENT ROUTES
// ============================================================================

/**
 * Get User Profile
 */
app.get('/api/user/profile', authenticateToken, async (req, res) => {
    try {
        const profile = await userService.getUserProfile(req.user.id);

        res.json({
            success: true,
            user: profile
        });

    } catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Update User Basic Information
 */
app.put('/api/user/info', authenticateToken, async (req, res) => {
    try {
        const result = await userService.updateUserInfo(req.user.id, req.body);

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Update user info error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Update User Profile
 */
app.put('/api/user/profile', authenticateToken, async (req, res) => {
    try {
        const result = await userService.updateUserProfile(req.user.id, req.body);

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Update profile error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Update Notification Preferences
 */
app.put('/api/user/preferences', authenticateToken, async (req, res) => {
    try {
        const result = await userService.updateNotificationPreferences(req.user.id, req.body);

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Update preferences error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Change Password
 */
app.put('/api/user/password', authenticateToken, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;

        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                success: false,
                error: { message: 'Current password and new password are required' }
            });
        }

        const result = await userService.changePassword(req.user.id, currentPassword, newPassword);

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Change password error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Add Address
 */
app.post('/api/user/addresses', authenticateToken, async (req, res) => {
    try {
        const result = await userService.addAddress(req.user.id, req.body);

        res.status(201).json({
            success: true,
            message: result.message,
            addressId: result.addressId
        });

    } catch (error) {
        console.error('Add address error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Update Address
 */
app.put('/api/user/addresses/:addressId', authenticateToken, async (req, res) => {
    try {
        const { addressId } = req.params;
        const result = await userService.updateAddress(req.user.id, parseInt(addressId), req.body);

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Update address error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Delete Address
 */
app.delete('/api/user/addresses/:addressId', authenticateToken, async (req, res) => {
    try {
        const { addressId } = req.params;
        const result = await userService.deleteAddress(req.user.id, parseInt(addressId));

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Delete address error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Get User Activity Log
 */
app.get('/api/user/activity', authenticateToken, async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 50;
        const activities = await userService.getUserActivity(req.user.id, limit);

        res.json({
            success: true,
            activities
        });

    } catch (error) {
        console.error('Get activity error:', error);
        res.status(500).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Get User Statistics
 */
app.get('/api/user/stats', authenticateToken, async (req, res) => {
    try {
        const stats = await userService.getUserStats(req.user.id);

        res.json({
            success: true,
            stats
        });

    } catch (error) {
        console.error('Get stats error:', error);
        res.status(500).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Get User Sessions
 */
app.get('/api/user/sessions', authenticateToken, async (req, res) => {
    try {
        const sessions = await authService.getUserSessions(req.user.id);

        res.json({
            success: true,
            sessions
        });

    } catch (error) {
        console.error('Get sessions error:', error);
        res.status(500).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Revoke Session
 */
app.delete('/api/user/sessions/:sessionId', authenticateToken, async (req, res) => {
    try {
        const { sessionId } = req.params;
        const result = await authService.revokeSession(req.user.id, parseInt(sessionId));

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Revoke session error:', error);
        res.status(400).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Revoke All Sessions
 */
app.delete('/api/user/sessions', authenticateToken, async (req, res) => {
    try {
        const result = await authService.revokeAllSessions(req.user.id);

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Revoke all sessions error:', error);
        res.status(500).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Deactivate Account
 */
app.put('/api/user/deactivate', authenticateToken, async (req, res) => {
    try {
        const { reason } = req.body;
        const result = await userService.deactivateAccount(req.user.id, reason);

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Deactivate account error:', error);
        res.status(500).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Delete Account Permanently
 */
app.delete('/api/user/account', authenticateToken, async (req, res) => {
    try {
        const result = await userService.deleteAccount(req.user.id);

        res.json({
            success: true,
            message: result.message
        });

    } catch (error) {
        console.error('Delete account error:', error);
        res.status(500).json({
            success: false,
            error: { message: error.message }
        });
    }
});

// ============================================================================
// ADMIN ROUTES (Optional)
// ============================================================================

/**
 * Get Database Statistics (Admin only)
 */
app.get('/api/admin/stats', authenticateToken, async (req, res) => {
    try {
        // Check if user is admin (you can implement this check)
        // For now, we'll allow any authenticated user

        const stats = await db.getStats();

        res.json({
            success: true,
            stats
        });

    } catch (error) {
        console.error('Get admin stats error:', error);
        res.status(500).json({
            success: false,
            error: { message: error.message }
        });
    }
});

/**
 * Database Cleanup (Admin only)
 */
app.post('/api/admin/cleanup', authenticateToken, async (req, res) => {
    try {
        // Check if user is admin

        await db.cleanup();

        res.json({
            success: true,
            message: 'Database cleanup completed successfully'
        });

    } catch (error) {
        console.error('Database cleanup error:', error);
        res.status(500).json({
            success: false,
            error: { message: error.message }
        });
    }
});

// ============================================================================
// ERROR HANDLING
// ============================================================================

/**
 * 404 Handler
 */
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        error: { message: 'Endpoint not found' }
    });
});

/**
 * Global Error Handler
 */
app.use((error, req, res, next) => {
    console.error('Global error handler:', error);

    res.status(500).json({
        success: false,
        error: {
            message: process.env.NODE_ENV === 'production'
                ? 'Internal server error'
                : error.message
        }
    });
});

// ============================================================================
// SERVER INITIALIZATION
// ============================================================================

/**
 * Initialize database and start server
 */
async function startServer() {
    try {
        console.log('🚀 Starting Authentication Server...');

        // Initialize database
        await db.initialize();

        // Schedule cleanup every hour
        setInterval(async () => {
            try {
                await db.cleanup();
            } catch (error) {
                console.error('Scheduled cleanup error:', error);
            }
        }, 60 * 60 * 1000); // 1 hour

        // Start server
        app.listen(PORT, () => {
            console.log(`✅ Authentication Server running on port ${PORT}`);
            console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
            console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
            console.log('');
            console.log('📋 Available Endpoints:');
            console.log('   Authentication:');
            console.log('   POST /api/auth/register - User registration');
            console.log('   POST /api/auth/login - User login');
            console.log('   GET  /api/auth/verify - Verify token');
            console.log('   POST /api/auth/logout - User logout');
            console.log('   POST /api/auth/verify-email - Email verification');
            console.log('   POST /api/auth/forgot-password - Request password reset');
            console.log('   POST /api/auth/reset-password - Reset password');
            console.log('');
            console.log('   User Management:');
            console.log('   GET  /api/user/profile - Get user profile');
            console.log('   PUT  /api/user/info - Update user info');
            console.log('   PUT  /api/user/profile - Update user profile');
            console.log('   PUT  /api/user/preferences - Update preferences');
            console.log('   PUT  /api/user/password - Change password');
            console.log('   POST /api/user/addresses - Add address');
            console.log('   PUT  /api/user/addresses/:id - Update address');
            console.log('   DEL  /api/user/addresses/:id - Delete address');
            console.log('   GET  /api/user/activity - Get activity log');
            console.log('   GET  /api/user/stats - Get user statistics');
            console.log('   GET  /api/user/sessions - Get user sessions');
            console.log('   DEL  /api/user/sessions/:id - Revoke session');
            console.log('   DEL  /api/user/sessions - Revoke all sessions');
            console.log('   PUT  /api/user/deactivate - Deactivate account');
            console.log('   DEL  /api/user/account - Delete account');
            console.log('');
            console.log('🔒 All user endpoints require Authorization: Bearer <token>');
        });

    } catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}

/**
 * Graceful shutdown
 */
process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down server...');

    try {
        await db.close();
        console.log('✅ Database connection closed');
        process.exit(0);
    } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');

    try {
        await db.close();
        console.log('✅ Database connection closed');
        process.exit(0);
    } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
});

// Start the server
startServer();

module.exports = app;
