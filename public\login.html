<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sports Malta</title>
    <meta name="description" content="Login to your Sports Malta account to book sports facilities across Malta.">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="auth-body">
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="nav-container">
                <div class="nav-logo">
                    <a href="index.html">
                        <span class="logo-text">SM</span>
                        <span class="logo-subtitle">Sports Malta</span>
                    </a>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="index.html" class="nav-link">Home</a>
                        </li>
                        <li class="nav-item">
                            <a href="booking.html" class="nav-link">Book Now</a>
                        </li>
                        <li class="nav-item">
                            <a href="contact.html" class="nav-link">Contact</a>
                        </li>
                        <li class="nav-item">
                            <a href="signup.html" class="nav-link">Sign Up</a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h1 class="auth-title">Welcome Back</h1>
                    <p class="auth-subtitle">Sign in to your Sports Malta account</p>
                </div>

                <!-- Login Form -->
                <form class="auth-form" id="loginForm">
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-input" 
                            placeholder="Enter your email"
                            required
                            autocomplete="email"
                        >
                        <div class="form-error" id="email-error"></div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <div class="password-input-container">
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="form-input" 
                                placeholder="Enter your password"
                                required
                                autocomplete="current-password"
                            >
                            <button type="button" class="password-toggle" id="passwordToggle">
                                <span class="password-toggle-icon">👁️</span>
                            </button>
                        </div>
                        <div class="form-error" id="password-error"></div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="rememberMe" name="rememberMe">
                            <span class="checkbox-checkmark"></span>
                            <span class="checkbox-label">Remember me</span>
                        </label>
                        <a href="forgot-password.html" class="forgot-password-link">Forgot password?</a>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full" id="loginButton">
                        <span class="btn-text">Sign In</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="loading-spinner"></span>
                            Signing in...
                        </span>
                    </button>

                    <div class="form-error" id="form-error"></div>
                    <div class="form-success" id="form-success"></div>
                </form>

                <!-- Social Login -->
                <div class="social-login">
                    <div class="social-divider">
                        <span class="social-divider-text">or continue with</span>
                    </div>
                    <div class="social-buttons">
                        <button type="button" class="btn btn-social" id="googleLogin">
                            <span class="social-icon">🔍</span>
                            Google
                        </button>
                        <button type="button" class="btn btn-social" id="facebookLogin">
                            <span class="social-icon">📘</span>
                            Facebook
                        </button>
                    </div>
                </div>

                <!-- Toggle Forms -->
                <div class="auth-footer">
                    <p class="auth-footer-text">
                        Don't have an account?
                        <a href="#" class="auth-link" id="showRegister">Sign up for free</a>
                    </p>
                </div>

                <!-- Registration Form (Hidden by default) -->
                <form class="auth-form" id="registerForm" style="display: none;">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="regFirstName" class="form-label">First Name</label>
                            <input
                                type="text"
                                id="regFirstName"
                                name="firstName"
                                class="form-input"
                                placeholder="Enter your first name"
                                required
                            >
                            <div class="form-error" id="regFirstName-error"></div>
                        </div>
                        <div class="form-group">
                            <label for="regLastName" class="form-label">Last Name</label>
                            <input
                                type="text"
                                id="regLastName"
                                name="lastName"
                                class="form-input"
                                placeholder="Enter your last name"
                                required
                            >
                            <div class="form-error" id="regLastName-error"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="regEmail" class="form-label">Email Address</label>
                        <input
                            type="email"
                            id="regEmail"
                            name="email"
                            class="form-input"
                            placeholder="Enter your email"
                            required
                            autocomplete="email"
                        >
                        <div class="form-error" id="regEmail-error"></div>
                    </div>

                    <div class="form-group">
                        <label for="regPhone" class="form-label">Phone Number</label>
                        <input
                            type="tel"
                            id="regPhone"
                            name="phone"
                            class="form-input"
                            placeholder="+356 1234 5678"
                            required
                        >
                        <div class="form-error" id="regPhone-error"></div>
                    </div>

                    <div class="form-group">
                        <label for="regPassword" class="form-label">Password</label>
                        <div class="password-input-container">
                            <input
                                type="password"
                                id="regPassword"
                                name="password"
                                class="form-input"
                                placeholder="Create a strong password"
                                required
                                autocomplete="new-password"
                            >
                            <button type="button" class="password-toggle" id="regPasswordToggle">
                                <span class="password-toggle-icon">👁️</span>
                            </button>
                        </div>
                        <div class="password-strength" id="regPasswordStrength"></div>
                        <div class="form-error" id="regPassword-error"></div>
                    </div>

                    <div class="form-group">
                        <label for="regConfirmPassword" class="form-label">Confirm Password</label>
                        <div class="password-input-container">
                            <input
                                type="password"
                                id="regConfirmPassword"
                                name="confirmPassword"
                                class="form-input"
                                placeholder="Confirm your password"
                                required
                                autocomplete="new-password"
                            >
                            <button type="button" class="password-toggle" id="regConfirmPasswordToggle">
                                <span class="password-toggle-icon">👁️</span>
                            </button>
                        </div>
                        <div class="form-error" id="regConfirmPassword-error"></div>
                    </div>



                    <button type="submit" class="btn btn-primary btn-full" id="registerButton">
                        <span class="btn-text">Create Account</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="loading-spinner"></span>
                            Creating account...
                        </span>
                    </button>

                    <div class="form-error" id="register-form-error"></div>
                    <div class="form-success" id="register-form-success"></div>
                </form>

                <!-- Back to Login Link (Hidden by default) -->
                <div class="auth-footer" id="backToLogin" style="display: none;">
                    <p class="auth-footer-text">
                        Already have an account?
                        <a href="#" class="auth-link" id="showLogin">Sign in here</a>
                    </p>
                </div>
            </div>

            <!-- Features Section -->
            <div class="auth-features">
                <h2 class="features-title">Why Choose Sports Malta?</h2>
                <div class="features-list">
                    <div class="feature-item">
                        <div class="feature-icon">⚽</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Premium Facilities</h3>
                            <p class="feature-description">Access to top-quality sports facilities across Malta</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📱</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Easy Booking</h3>
                            <p class="feature-description">Book your favorite sports venues in just a few clicks</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">💰</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Best Prices</h3>
                            <p class="feature-description">Competitive pricing with no hidden fees</p>
                        </div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🏆</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Trusted Platform</h3>
                            <p class="feature-description">Secure payments and reliable booking system</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <span class="logo-text">SM</span>
                        <span class="logo-subtitle">Sports Malta</span>
                    </div>
                    <p class="footer-description">Your premier destination for booking sports facilities across Malta.</p>
                </div>
                
                <div class="footer-section">
                    <h3 class="footer-title">Quick Links</h3>
                    <ul class="footer-links">
                        <li><a href="booking.html">Book Now</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="signup.html">Sign Up</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3 class="footer-title">Contact</h3>
                    <div class="contact-info">
                        <p>📧 <EMAIL></p>
                        <p>📞 +356 1234 5678</p>
                        <p>📍 Malta</p>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Sports Malta. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Modal for Success/Error Messages -->
    <div id="authModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Message</h3>
                <span class="modal-close" id="modalClose">&times;</span>
            </div>
            <div class="modal-body">
                <div id="modalIcon" class="modal-icon"></div>
                <p id="modalMessage">Message content</p>
            </div>
            <div class="modal-footer">
                <button id="modalButton" class="btn btn-primary">OK</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/enhanced-login.js"></script>
    <script>
        // Initialize enhanced login page
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in
            const authToken = localStorage.getItem('authToken');
            const currentUser = localStorage.getItem('currentUser');

            if (authToken && currentUser) {
                // User is already logged in, redirect to account page
                const returnUrl = new URLSearchParams(window.location.search).get('returnUrl') || '/account.html';
                window.location.href = returnUrl;
                return;
            }

            // Initialize enhanced login manager
            window.enhancedLoginManager = new EnhancedLoginManager();

            // Show register form if URL parameter is set
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('register') === 'true') {
                window.enhancedLoginManager.toggleForms('register');
            }
        });
    </script>
</body>
</html>
