const FacilityService = require('../../server/services/facilityService');
const { Facility } = require('../../server/models');
const { pool } = require('../../server/config/database');

// Mock the database pool
jest.mock('../../server/config/database', () => ({
    pool: {
        query: jest.fn()
    }
}));

// Mock the models
jest.mock('../../server/models', () => ({
    Facility: {
        findById: jest.fn(),
        getSirensMainFootballFacility: jest.fn(),
        getSirensFootballFacilities: jest.fn()
    }
}));

describe('FacilityService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('getSirensMainFacility', () => {
        test('should return main Sirens facility', async () => {
            const mockFacility = {
                id: 'facility-1',
                name: 'Sirens FC Main Pitch',
                sportType: 'football'
            };
            
            Facility.getSirensMainFootballFacility.mockResolvedValue(mockFacility);

            const result = await FacilityService.getSirensMainFacility();

            expect(result).toEqual(mockFacility);
            expect(Facility.getSirensMainFootballFacility).toHaveBeenCalled();
        });

        test('should throw error when facility not found', async () => {
            Facility.getSirensMainFootballFacility.mockResolvedValue(null);

            await expect(FacilityService.getSirensMainFacility()).rejects.toThrow('Sirens FC football facility not found');
        });

        test('should handle database errors', async () => {
            Facility.getSirensMainFootballFacility.mockRejectedValue(new Error('Database error'));

            await expect(FacilityService.getSirensMainFacility()).rejects.toThrow('Database error');
        });
    });

    describe('getSirensFacilities', () => {
        test('should return all Sirens facilities', async () => {
            const mockFacilities = [
                { id: 'facility-1', name: 'Main Pitch', sportType: 'football' },
                { id: 'facility-2', name: 'Training Pitch', sportType: 'football' }
            ];
            
            Facility.getSirensFootballFacilities.mockResolvedValue(mockFacilities);

            const result = await FacilityService.getSirensFacilities();

            expect(result).toEqual(mockFacilities);
            expect(Facility.getSirensFootballFacilities).toHaveBeenCalled();
        });

        test('should handle database errors', async () => {
            Facility.getSirensFootballFacilities.mockRejectedValue(new Error('Database error'));

            await expect(FacilityService.getSirensFacilities()).rejects.toThrow('Database error');
        });
    });

    describe('getFacilityById', () => {
        test('should return facility by ID', async () => {
            const mockFacility = {
                id: 'facility-1',
                name: 'Sirens FC Main Pitch',
                sportType: 'football'
            };
            
            Facility.findById.mockResolvedValue(mockFacility);

            const result = await FacilityService.getFacilityById('facility-1');

            expect(result).toEqual(mockFacility);
            expect(Facility.findById).toHaveBeenCalledWith('facility-1');
        });

        test('should throw error when facility not found', async () => {
            Facility.findById.mockResolvedValue(null);

            await expect(FacilityService.getFacilityById('nonexistent')).rejects.toThrow('Facility not found');
        });
    });

    describe('calculateBookingCost', () => {
        const mockFacility = {
            hourlyRatePeak: 60,
            hourlyRateOffPeak: 40,
            hourlyRateWeekend: 80
        };

        test('should calculate cost for weekday off-peak hours', () => {
            const result = FacilityService.calculateBookingCost(
                mockFacility,
                '2025-07-30', // Wednesday
                '14:00',
                2,
                'full'
            );

            expect(result).toEqual({
                baseRate: 40,
                rateType: 'off-peak',
                durationHours: 2,
                subtotal: 80,
                pitchConfiguration: 'full',
                configurationDescription: 'Full Pitch',
                configurationMultiplier: 1,
                configurationDiscount: 0,
                totalCost: 80,
                breakdown: {
                    baseHourlyRate: 40,
                    hours: 2,
                    rateType: 'off-peak',
                    pitchType: 'Full Pitch',
                    discount: 0
                }
            });
        });

        test('should calculate cost for weekday peak hours', () => {
            const result = FacilityService.calculateBookingCost(
                mockFacility,
                '2025-07-30', // Wednesday
                '18:00',
                1,
                'full'
            );

            expect(result).toEqual({
                baseRate: 60,
                rateType: 'peak',
                durationHours: 1,
                subtotal: 60,
                pitchConfiguration: 'full',
                configurationDescription: 'Full Pitch',
                configurationMultiplier: 1,
                configurationDiscount: 0,
                totalCost: 60,
                breakdown: {
                    baseHourlyRate: 60,
                    hours: 1,
                    rateType: 'peak',
                    pitchType: 'Full Pitch',
                    discount: 0
                }
            });
        });

        test('should calculate cost for weekend', () => {
            const result = FacilityService.calculateBookingCost(
                mockFacility,
                '2025-08-02', // Saturday
                '14:00',
                1,
                'full'
            );

            expect(result).toEqual({
                baseRate: 80,
                rateType: 'weekend',
                durationHours: 1,
                subtotal: 80,
                pitchConfiguration: 'full',
                configurationDescription: 'Full Pitch',
                configurationMultiplier: 1,
                configurationDiscount: 0,
                totalCost: 80,
                breakdown: {
                    baseHourlyRate: 80,
                    hours: 1,
                    rateType: 'weekend',
                    pitchType: 'Full Pitch',
                    discount: 0
                }
            });
        });

        test('should apply half-pitch discount for left side', () => {
            const result = FacilityService.calculateBookingCost(
                mockFacility,
                '2025-07-30',
                '14:00',
                2,
                'left'
            );

            expect(result).toEqual({
                baseRate: 40,
                rateType: 'off-peak',
                durationHours: 2,
                subtotal: 80,
                pitchConfiguration: 'left',
                configurationDescription: 'Left Side',
                configurationMultiplier: 0.6,
                configurationDiscount: 32,
                totalCost: 48,
                breakdown: {
                    baseHourlyRate: 40,
                    hours: 2,
                    rateType: 'off-peak',
                    pitchType: 'Left Side',
                    discount: 32
                }
            });
        });

        test('should apply half-pitch discount for right side', () => {
            const result = FacilityService.calculateBookingCost(
                mockFacility,
                '2025-07-30',
                '14:00',
                1,
                'right'
            );

            expect(result).toEqual({
                baseRate: 40,
                rateType: 'off-peak',
                durationHours: 1,
                subtotal: 40,
                pitchConfiguration: 'right',
                configurationDescription: 'Right Side',
                configurationMultiplier: 0.6,
                configurationDiscount: 16,
                totalCost: 24,
                breakdown: {
                    baseHourlyRate: 40,
                    hours: 1,
                    rateType: 'off-peak',
                    pitchType: 'Right Side',
                    discount: 16
                }
            });
        });

        test('should handle early morning peak hours', () => {
            const result = FacilityService.calculateBookingCost(
                mockFacility,
                '2025-07-30',
                '08:00',
                1,
                'full'
            );

            expect(result.rateType).toBe('peak');
            expect(result.baseRate).toBe(60);
        });

        test('should handle calculation errors gracefully', () => {
            expect(() => {
                FacilityService.calculateBookingCost(
                    null,
                    '2025-07-30',
                    '14:00',
                    1,
                    'full'
                );
            }).toThrow();
        });
    });

    describe('validateOperatingHours', () => {
        const mockFacility = {
            getOperatingHours: jest.fn()
        };

        test('should validate hours within operating range', () => {
            mockFacility.getOperatingHours.mockReturnValue({
                open: '08:00',
                close: '22:00'
            });

            const result = FacilityService.validateOperatingHours(
                mockFacility,
                '2025-07-30',
                '10:00',
                '11:00'
            );

            expect(result).toEqual({
                valid: true,
                operatingHours: {
                    open: '08:00',
                    close: '22:00'
                }
            });
        });

        test('should reject when facility is closed', () => {
            mockFacility.getOperatingHours.mockReturnValue(null);

            const result = FacilityService.validateOperatingHours(
                mockFacility,
                '2025-07-30',
                '10:00',
                '11:00'
            );

            expect(result).toEqual({
                valid: false,
                reason: 'Facility is closed on this day'
            });
        });

        test('should reject when start time is before opening', () => {
            mockFacility.getOperatingHours.mockReturnValue({
                open: '08:00',
                close: '22:00'
            });

            const result = FacilityService.validateOperatingHours(
                mockFacility,
                '2025-07-30',
                '07:00',
                '08:00'
            );

            expect(result).toEqual({
                valid: false,
                reason: 'Facility opens at 08:00'
            });
        });

        test('should reject when end time is after closing', () => {
            mockFacility.getOperatingHours.mockReturnValue({
                open: '08:00',
                close: '22:00'
            });

            const result = FacilityService.validateOperatingHours(
                mockFacility,
                '2025-07-30',
                '21:00',
                '23:00'
            );

            expect(result).toEqual({
                valid: false,
                reason: 'Facility closes at 22:00'
            });
        });
    });

    describe('supportsPitchConfigurations', () => {
        test('should return true for football facilities', () => {
            const facility = { sportType: 'football' };
            const result = FacilityService.supportsPitchConfigurations(facility);
            expect(result).toBe(true);
        });

        test('should return false for non-football facilities', () => {
            const facility = { sportType: 'tennis' };
            const result = FacilityService.supportsPitchConfigurations(facility);
            expect(result).toBe(false);
        });
    });

    describe('getAvailablePitchConfigurations', () => {
        test('should return all configurations for football facilities', () => {
            const facility = { sportType: 'football' };
            const result = FacilityService.getAvailablePitchConfigurations(facility);
            expect(result).toEqual(['full', 'left', 'right']);
        });

        test('should return only full for non-football facilities', () => {
            const facility = { sportType: 'tennis' };
            const result = FacilityService.getAvailablePitchConfigurations(facility);
            expect(result).toEqual(['full']);
        });
    });

    describe('validatePitchConfiguration', () => {
        test('should validate correct configuration for football', () => {
            const facility = { sportType: 'football' };
            const result = FacilityService.validatePitchConfiguration(facility, 'left');
            
            expect(result).toEqual({
                valid: true,
                configuration: 'left'
            });
        });

        test('should reject invalid configuration', () => {
            const facility = { sportType: 'football' };
            const result = FacilityService.validatePitchConfiguration(facility, 'invalid');
            
            expect(result).toEqual({
                valid: false,
                reason: 'Invalid pitch configuration. Available options: full, left, right'
            });
        });

        test('should reject half-pitch for non-football facilities', () => {
            const facility = { sportType: 'tennis' };
            const result = FacilityService.validatePitchConfiguration(facility, 'left');
            
            expect(result).toEqual({
                valid: false,
                reason: 'Invalid pitch configuration. Available options: full'
            });
        });
    });

    describe('getFacilityAvailabilitySummary', () => {
        const mockFacility = {
            id: 'facility-1',
            name: 'Sirens FC Main Pitch',
            getOperatingHours: jest.fn()
        };

        beforeEach(() => {
            Facility.findById.mockResolvedValue(mockFacility);
        });

        test('should return availability summary for date range', async () => {
            mockFacility.getOperatingHours.mockReturnValue({
                open: '08:00',
                close: '22:00'
            });
            
            pool.query.mockResolvedValue({
                rows: [{ booking_count: '2' }]
            });

            const result = await FacilityService.getFacilityAvailabilitySummary(
                'facility-1',
                '2025-07-30',
                '2025-07-30'
            );

            expect(result).toMatchObject({
                facilityId: 'facility-1',
                facilityName: 'Sirens FC Main Pitch',
                startDate: '2025-07-30',
                endDate: '2025-07-30',
                summary: [{
                    date: '2025-07-30',
                    status: 'available',
                    availableSlots: 12, // 14 total slots - 2 bookings
                    totalSlots: 14,
                    operatingHours: {
                        open: '08:00',
                        close: '22:00'
                    }
                }]
            });
        });

        test('should mark days as closed when facility is closed', async () => {
            mockFacility.getOperatingHours.mockReturnValue(null);

            const result = await FacilityService.getFacilityAvailabilitySummary(
                'facility-1',
                '2025-07-30',
                '2025-07-30'
            );

            expect(result.summary[0]).toMatchObject({
                date: '2025-07-30',
                status: 'closed',
                availableSlots: 0,
                totalSlots: 0
            });
        });

        test('should mark days as full when no slots available', async () => {
            mockFacility.getOperatingHours.mockReturnValue({
                open: '08:00',
                close: '22:00'
            });
            
            pool.query.mockResolvedValue({
                rows: [{ booking_count: '14' }]
            });

            const result = await FacilityService.getFacilityAvailabilitySummary(
                'facility-1',
                '2025-07-30',
                '2025-07-30'
            );

            expect(result.summary[0]).toMatchObject({
                date: '2025-07-30',
                status: 'full',
                availableSlots: 0,
                totalSlots: 14
            });
        });
    });

    describe('getFacilityDetails', () => {
        test('should return detailed facility information', async () => {
            const mockFacility = {
                id: 'facility-1',
                name: 'Sirens FC Main Pitch',
                sportType: 'football',
                facilityType: 'outdoor',
                capacity: 22,
                features: ['floodlights', 'changing_rooms'],
                surfaceType: 'grass',
                hourlyRatePeak: 60,
                hourlyRateOffPeak: 40,
                hourlyRateWeekend: 80,
                operatingHours: { monday: { open: '08:00', close: '22:00' } },
                isActive: true
            };
            
            Facility.findById.mockResolvedValue(mockFacility);

            const result = await FacilityService.getFacilityDetails('facility-1');

            expect(result).toEqual({
                id: 'facility-1',
                name: 'Sirens FC Main Pitch',
                sportType: 'football',
                facilityType: 'outdoor',
                capacity: 22,
                features: ['floodlights', 'changing_rooms'],
                surfaceType: 'grass',
                pricing: {
                    peak: 60,
                    offPeak: 40,
                    weekend: 80,
                    halfPitchMultiplier: 0.6
                },
                operatingHours: { monday: { open: '08:00', close: '22:00' } },
                isActive: true
            });
        });
    });

    describe('getFacilityStats', () => {
        const mockFacility = {
            id: 'facility-1',
            name: 'Sirens FC Main Pitch'
        };

        beforeEach(() => {
            Facility.findById.mockResolvedValue(mockFacility);
        });

        test('should return facility statistics', async () => {
            pool.query.mockResolvedValue({
                rows: [{
                    total_bookings: '10',
                    confirmed_bookings: '8',
                    cancelled_bookings: '2',
                    total_revenue: '500.00',
                    average_booking_cost: '50.00',
                    total_hours_booked: '12.5'
                }]
            });

            const result = await FacilityService.getFacilityStats(
                'facility-1',
                '2025-07-01',
                '2025-07-31'
            );

            expect(result).toEqual({
                facilityId: 'facility-1',
                facilityName: 'Sirens FC Main Pitch',
                period: { startDate: '2025-07-01', endDate: '2025-07-31' },
                stats: {
                    totalBookings: 10,
                    confirmedBookings: 8,
                    cancelledBookings: 2,
                    totalRevenue: 500.00,
                    averageBookingCost: 50.00,
                    totalHoursBooked: 12.5,
                    cancellationRate: '20.00'
                }
            });
        });

        test('should handle zero bookings', async () => {
            pool.query.mockResolvedValue({
                rows: [{
                    total_bookings: '0',
                    confirmed_bookings: '0',
                    cancelled_bookings: '0',
                    total_revenue: null,
                    average_booking_cost: null,
                    total_hours_booked: null
                }]
            });

            const result = await FacilityService.getFacilityStats(
                'facility-1',
                '2025-07-01',
                '2025-07-31'
            );

            expect(result.stats).toMatchObject({
                totalBookings: 0,
                confirmedBookings: 0,
                cancelledBookings: 0,
                totalRevenue: 0,
                averageBookingCost: 0,
                totalHoursBooked: 0,
                cancellationRate: 0
            });
        });
    });

    describe('searchFacilities', () => {
        test('should search facilities by sport type', async () => {
            const mockResults = [{
                id: 'facility-1',
                name: 'Main Pitch',
                sport_type: 'football',
                venue_name: 'Sirens FC',
                address_city: 'Naxxar'
            }];
            
            pool.query.mockResolvedValue({ rows: mockResults });

            const result = await FacilityService.searchFacilities({
                sportType: 'football'
            });

            expect(result).toEqual([{
                id: 'facility-1',
                name: 'Main Pitch',
                sport_type: 'football',
                venue_name: 'Sirens FC',
                address_city: 'Naxxar',
                location: 'Naxxar'
            }]);

            expect(pool.query).toHaveBeenCalledWith(
                expect.stringContaining('f.sport_type = $1'),
                ['football']
            );
        });

        test('should search facilities by location', async () => {
            pool.query.mockResolvedValue({ rows: [] });

            await FacilityService.searchFacilities({
                location: 'Naxxar'
            });

            expect(pool.query).toHaveBeenCalledWith(
                expect.stringContaining('v.address_city ILIKE $1'),
                ['%Naxxar%']
            );
        });

        test('should search facilities by price range', async () => {
            pool.query.mockResolvedValue({ rows: [] });

            await FacilityService.searchFacilities({
                priceRange: { min: 30, max: 60 }
            });

            expect(pool.query).toHaveBeenCalledWith(
                expect.stringContaining('f.hourly_rate_off_peak >= $1'),
                [30, 60]
            );
        });

        test('should search facilities by features', async () => {
            pool.query.mockResolvedValue({ rows: [] });

            await FacilityService.searchFacilities({
                features: ['floodlights', 'parking']
            });

            expect(pool.query).toHaveBeenCalledWith(
                expect.stringContaining('f.features && $1'),
                [['floodlights', 'parking']]
            );
        });

        test('should handle search with no criteria', async () => {
            pool.query.mockResolvedValue({ rows: [] });

            await FacilityService.searchFacilities();

            expect(pool.query).toHaveBeenCalledWith(
                expect.stringContaining('WHERE f.is_active = true'),
                []
            );
        });
    });
});