const express = require('express');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const Booking = require('../models/Booking');
const { verifyToken } = require('../middleware/auth');
const emailService = require('../services/emailService');
const database = require('../config/database');

const router = express.Router();

// Rate limiting for booking endpoints
const bookingLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // limit each IP to 10 booking requests per windowMs
    message: {
        success: false,
        error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Too many booking attempts, please try again later'
        }
    },
    standardHeaders: true,
    legacyHeaders: false,
});

// Validation rules for creating a booking
const createBookingValidation = [
    body('facilityId')
        .isInt({ min: 1 })
        .withMessage('Valid facility ID is required'),
    body('date')
        .isISO8601()
        .withMessage('Valid date is required (YYYY-MM-DD format)')
        .custom((value) => {
            const bookingDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (bookingDate < today) {
                throw new Error('Booking date cannot be in the past');
            }
            
            // Limit bookings to 3 months in advance
            const maxDate = new Date();
            maxDate.setMonth(maxDate.getMonth() + 3);
            
            if (bookingDate > maxDate) {
                throw new Error('Bookings can only be made up to 3 months in advance');
            }
            
            return true;
        }),
    body('startTime')
        .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
        .withMessage('Valid start time is required (HH:MM format)'),
    body('endTime')
        .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
        .withMessage('Valid end time is required (HH:MM format)')
        .custom((value, { req }) => {
            const startTime = req.body.startTime;
            if (startTime && value <= startTime) {
                throw new Error('End time must be after start time');
            }
            return true;
        }),
    body('pitchConfiguration')
        .isIn(['full', 'half_left', 'half_right'])
        .withMessage('Valid pitch configuration is required'),
    body('notes')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Notes cannot exceed 500 characters')
];

/**
 * POST /api/bookings
 * Create a new booking
 */
router.post('/', verifyToken, bookingLimiter, createBookingValidation, async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Invalid input data',
                    details: errors.array()
                }
            });
        }

        const { facilityId, date, startTime, endTime, pitchConfiguration, notes } = req.body;
        const userId = req.user.id;

        // Check if facility exists
        const facility = await database.get('SELECT * FROM facilities WHERE id = ? AND is_active = 1', [facilityId]);
        if (!facility) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'FACILITY_NOT_FOUND',
                    message: 'Facility not found or inactive'
                }
            });
        }

        // Check for conflicting bookings
        const conflicts = await Booking.findConflictingBookings(
            facilityId, date, startTime, endTime, pitchConfiguration
        );

        if (conflicts.length > 0) {
            return res.status(409).json({
                success: false,
                error: {
                    code: 'TIME_SLOT_UNAVAILABLE',
                    message: 'The selected time slot is not available',
                    conflicts: conflicts.map(booking => ({
                        date: booking.date,
                        startTime: booking.startTime,
                        endTime: booking.endTime,
                        pitchConfiguration: booking.pitchConfiguration
                    }))
                }
            });
        }

        // Calculate total amount based on pitch configuration and duration
        const duration = calculateDuration(startTime, endTime);
        const hourlyRate = pitchConfiguration === 'full' ? facility.pricing_full : facility.pricing_half;
        const totalAmount = (duration / 60) * hourlyRate;

        // Create booking
        const booking = await Booking.create({
            userId,
            facilityId,
            date,
            startTime,
            endTime,
            pitchConfiguration,
            notes,
            totalAmount
        });

        // Send email notifications
        try {
            const emailResult = await emailService.sendBookingConfirmation(booking);
            console.log('Email notification result:', emailResult);
        } catch (emailError) {
            console.error('Failed to send email notification:', emailError);
            // Don't fail the booking if email fails
        }

        res.status(201).json({
            success: true,
            data: {
                booking: booking.toJSON()
            }
        });

    } catch (error) {
        console.error('Create booking error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'An internal server error occurred'
            }
        });
    }
});

/**
 * GET /api/bookings
 * Get user's bookings
 */
router.get('/', verifyToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const limit = parseInt(req.query.limit) || 50;

        const bookings = await Booking.findByUserId(userId, limit);

        res.json({
            success: true,
            data: {
                bookings: bookings.map(booking => booking.toJSON())
            }
        });

    } catch (error) {
        console.error('Get bookings error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'An internal server error occurred'
            }
        });
    }
});

/**
 * GET /api/bookings/:id
 * Get a specific booking
 */
router.get('/:id', verifyToken, async (req, res) => {
    try {
        const bookingId = req.params.id;
        const userId = req.user.id;

        const booking = await Booking.findById(bookingId);

        if (!booking) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'BOOKING_NOT_FOUND',
                    message: 'Booking not found'
                }
            });
        }

        // Check if user owns this booking
        if (booking.userId !== userId) {
            return res.status(403).json({
                success: false,
                error: {
                    code: 'ACCESS_DENIED',
                    message: 'You can only view your own bookings'
                }
            });
        }

        res.json({
            success: true,
            data: {
                booking: booking.toJSON()
            }
        });

    } catch (error) {
        console.error('Get booking error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'An internal server error occurred'
            }
        });
    }
});

/**
 * PUT /api/bookings/:id/cancel
 * Cancel a booking
 */
router.put('/:id/cancel', verifyToken, async (req, res) => {
    try {
        const bookingId = req.params.id;
        const userId = req.user.id;

        const booking = await Booking.findById(bookingId);

        if (!booking) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'BOOKING_NOT_FOUND',
                    message: 'Booking not found'
                }
            });
        }

        // Check if user owns this booking
        if (booking.userId !== userId) {
            return res.status(403).json({
                success: false,
                error: {
                    code: 'ACCESS_DENIED',
                    message: 'You can only cancel your own bookings'
                }
            });
        }

        // Check if booking can be cancelled
        if (booking.status === 'cancelled') {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'ALREADY_CANCELLED',
                    message: 'Booking is already cancelled'
                }
            });
        }

        // Check if booking is in the past
        const bookingDateTime = new Date(`${booking.date} ${booking.startTime}`);
        const now = new Date();
        
        if (bookingDateTime < now) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'CANNOT_CANCEL_PAST_BOOKING',
                    message: 'Cannot cancel bookings that have already started'
                }
            });
        }

        // Cancel the booking
        await booking.cancel();

        // Send cancellation email notifications
        try {
            const emailResult = await emailService.sendBookingCancellation(booking);
            console.log('Cancellation email result:', emailResult);
        } catch (emailError) {
            console.error('Failed to send cancellation email:', emailError);
            // Don't fail the cancellation if email fails
        }

        res.json({
            success: true,
            data: {
                booking: booking.toJSON()
            }
        });

    } catch (error) {
        console.error('Cancel booking error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'An internal server error occurred'
            }
        });
    }
});

/**
 * GET /api/bookings/facilities
 * Get all available facilities
 */
router.get('/facilities', async (req, res) => {
    try {
        const db = database.getDb();
        const facilities = await new Promise((resolve, reject) => {
            db.all('SELECT * FROM facilities WHERE is_active = 1 ORDER BY name', (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        res.json({
            success: true,
            data: { facilities }
        });
    } catch (error) {
        console.error('Error fetching facilities:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'INTERNAL_ERROR',
                message: 'Failed to fetch facilities'
            }
        });
    }
});

/**
 * GET /api/bookings/availability/:facilityId
 * Get availability for a facility on a specific date
 */
router.get('/availability/:facilityId', async (req, res) => {
    try {
        const facilityId = req.params.facilityId;
        const date = req.query.date;

        if (!date) {
            return res.status(400).json({
                success: false,
                error: {
                    code: 'DATE_REQUIRED',
                    message: 'Date parameter is required'
                }
            });
        }

        // Check if facility exists
        const facility = await database.get('SELECT * FROM facilities WHERE id = ? AND is_active = 1', [facilityId]);
        if (!facility) {
            return res.status(404).json({
                success: false,
                error: {
                    code: 'FACILITY_NOT_FOUND',
                    message: 'Facility not found or inactive'
                }
            });
        }

        const bookings = await Booking.getAvailability(facilityId, date);

        res.json({
            success: true,
            data: {
                facility: {
                    id: facility.id,
                    name: facility.name,
                    pricingFull: facility.pricing_full,
                    pricingHalf: facility.pricing_half
                },
                date: date,
                bookings: bookings.map(booking => ({
                    startTime: booking.startTime,
                    endTime: booking.endTime,
                    pitchConfiguration: booking.pitchConfiguration
                }))
            }
        });

    } catch (error) {
        console.error('Get availability error:', error);
        res.status(500).json({
            success: false,
            error: {
                code: 'SERVER_ERROR',
                message: 'An internal server error occurred'
            }
        });
    }
});

// Helper function to calculate duration in minutes
function calculateDuration(startTime, endTime) {
    const start = new Date(`2000-01-01 ${startTime}`);
    const end = new Date(`2000-01-01 ${endTime}`);
    return (end - start) / (1000 * 60); // minutes
}

module.exports = router;
