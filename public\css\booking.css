/* Booking Page Specific Styles */

.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Booking Steps */
.booking-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.step {
    display: flex;
    align-items: center;
    margin: 0 1rem;
    opacity: 0.5;
    transition: var(--transition);
}

.step.active {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--border-color);
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 0.5rem;
    transition: var(--transition);
}

.step.active .step-number {
    background: var(--primary-color);
    color: white;
}

.step-text {
    font-weight: 500;
    white-space: nowrap;
}

.step:not(:last-child)::after {
    content: '';
    width: 50px;
    height: 2px;
    background: var(--border-color);
    margin-left: 1rem;
}

/* Sport Selection */
.sport-selection {
    padding: 3rem 0;
}

.sports-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.sport-selection-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.sport-selection-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.sport-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.sport-name {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.sport-info {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.sport-features {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.feature-tag {
    background: #f8f9fa;
    color: #333;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.price-range {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.btn-sport-select {
    background: var(--primary-color);
    color: white;
    width: 100%;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-sport-select:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

/* Popular Venues */
.popular-venues {
    padding: 4rem 0;
    background: var(--background-light);
}

.venues-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.venue-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.venue-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.venue-image {
    height: 200px;
    overflow: hidden;
}

.venue-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.venue-card:hover .venue-image img {
    transform: scale(1.05);
}

.venue-content {
    padding: 1.5rem;
}

.venue-name {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.venue-location {
    color: var(--text-light);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.venue-sports {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.sport-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
}

.sport-badge.football {
    background: #28a745;
}

.sport-badge.tennis {
    background: #007bff;
}

.sport-badge.padel {
    background: #fd7e14;
}

.venue-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.venue-features .feature {
    background: var(--background-light);
    color: var(--text-color);
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.btn-venue {
    background: var(--primary-color);
    color: white;
    width: 100%;
    text-align: center;
    text-decoration: none;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn-venue:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

/* Quick Booking */
.quick-booking {
    padding: 4rem 0;
    background: var(--secondary-color);
    color: white;
}

.quick-booking-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.quick-booking-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.quick-booking-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.quick-booking-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: white;
}

.form-group select,
.form-group input {
    padding: 0.75rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: white;
    color: var(--text-color);
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 220, 48, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .booking-steps {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .step {
        margin: 0;
    }
    
    .step:not(:last-child)::after {
        display: none;
    }
    
    .step-text {
        font-size: 0.9rem;
    }
    
    .sports-selection-grid {
        grid-template-columns: 1fr;
    }
    
    .venues-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .page-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 2rem 0;
    }
    
    .sport-selection {
        padding: 2rem 0;
    }
    
    .popular-venues {
        padding: 3rem 0;
    }
    
    .quick-booking {
        padding: 3rem 0;
    }
    
    .quick-booking-form {
        padding: 1.5rem;
    }
}

/* Enhanced Booking Form Styles */
.simple-booking-form {
    padding: 2rem 0;
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.simple-booking-form h2 {
    text-align: center;
    color: var(--text-color);
    margin-bottom: 2rem;
    font-size: 2rem;
    font-weight: 600;
}

.booking-form {
    max-width: 600px;
    margin: 0 auto;
    display: grid;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    font-size: 1rem;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-light);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(0, 255, 153, 0.1);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

/* Pricing Display */
.pricing-display {
    background: rgba(0, 255, 153, 0.1);
    border: 1px solid rgba(0, 255, 153, 0.2);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1rem 0;
    display: none;
}

.pricing-display.show {
    display: block;
}

.pricing-info h4 {
    color: var(--primary-color);
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
}

.pricing-details {
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

/* Booking Total */
.booking-total {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1rem 0;
    display: none;
}

.booking-total.show {
    display: block;
}

.total-breakdown p {
    margin: 0.25rem 0;
    color: var(--text-light);
}

.total-amount {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 0.5rem;
    margin-top: 0.5rem !important;
}

/* Auth Required Message */
.auth-required {
    text-align: center;
    padding: 3rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-required h3 {
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.auth-required p {
    color: var(--text-light);
    margin-bottom: 2rem;
}

.auth-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.auth-buttons .btn {
    min-width: 120px;
}

/* Booking Notifications */
.booking-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    z-index: 1000;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    animation: slideInRight 0.3s ease-out;
}

.notification-error {
    background: rgba(255, 71, 87, 0.9);
    border: 1px solid rgba(255, 71, 87, 0.3);
}

.notification-success {
    background: rgba(0, 255, 153, 0.9);
    border: 1px solid rgba(0, 255, 153, 0.3);
}

.notification-info {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    gap: 1rem;
}

.notification-message {
    color: #1a1a1a;
    font-weight: 500;
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    color: #1a1a1a;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.notification-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading Spinner */
.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}