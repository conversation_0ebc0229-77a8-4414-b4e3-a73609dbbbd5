/**
 * User Service
 * Handles user profile management, account settings, and user data operations
 */

const bcrypt = require('bcrypt');
const validator = require('validator');
const { db } = require('../database/database');
const authService = require('./authService');

class UserService {
    constructor() {
        this.SALT_ROUNDS = 12;
    }

    /**
     * Get user profile with all related data
     */
    async getUserProfile(userId) {
        try {
            // Get basic user info
            const user = await db.get(`
                SELECT 
                    u.id, u.email, u.first_name, u.last_name, u.phone, u.date_of_birth,
                    u.is_active, u.is_verified, u.email_notifications, u.sms_notifications,
                    u.marketing_emails, u.language_preference, u.timezone,
                    u.created_at, u.updated_at, u.last_login_at,
                    p.bio, p.avatar_url, p.website, p.location, p.twitter_handle,
                    p.linkedin_url, p.github_username, p.profile_visibility,
                    p.show_email, p.show_phone
                FROM users u
                LEFT JOIN user_profiles p ON u.id = p.user_id
                WHERE u.id = ?
            `, [userId]);

            if (!user) {
                throw new Error('User not found');
            }

            // Get user addresses
            const addresses = await db.all(`
                SELECT * FROM user_addresses 
                WHERE user_id = ? AND is_active = 1
                ORDER BY is_default DESC, created_at DESC
            `, [userId]);

            // Get recent login attempts
            const recentLogins = await db.all(`
                SELECT ip_address, user_agent, attempted_at, success
                FROM login_attempts
                WHERE user_id = ? AND success = 1
                ORDER BY attempted_at DESC
                LIMIT 5
            `, [userId]);

            // Get active sessions
            const activeSessions = await authService.getUserSessions(userId);

            return {
                ...user,
                addresses,
                recentLogins,
                activeSessions,
                displayName: user.first_name && user.last_name ? 
                    `${user.first_name} ${user.last_name}` : user.email
            };

        } catch (error) {
            console.error('Error getting user profile:', error);
            throw error;
        }
    }

    /**
     * Update user basic information
     */
    async updateUserInfo(userId, updateData) {
        try {
            const { firstName, lastName, phone, dateOfBirth, languagePreference, timezone } = updateData;

            // Validate input
            if (firstName && (firstName.trim().length < 2 || firstName.trim().length > 50)) {
                throw new Error('First name must be between 2 and 50 characters');
            }

            if (lastName && (lastName.trim().length < 2 || lastName.trim().length > 50)) {
                throw new Error('Last name must be between 2 and 50 characters');
            }

            if (phone && !validator.isMobilePhone(phone, 'any')) {
                throw new Error('Please provide a valid phone number');
            }

            if (dateOfBirth && !validator.isDate(dateOfBirth)) {
                throw new Error('Please provide a valid date of birth');
            }

            // Get current user data for audit log
            const currentUser = await db.get('SELECT * FROM users WHERE id = ?', [userId]);
            if (!currentUser) {
                throw new Error('User not found');
            }

            // Build update query dynamically
            const updates = [];
            const params = [];

            if (firstName !== undefined) {
                updates.push('first_name = ?');
                params.push(firstName.trim());
            }
            if (lastName !== undefined) {
                updates.push('last_name = ?');
                params.push(lastName.trim());
            }
            if (phone !== undefined) {
                updates.push('phone = ?');
                params.push(phone || null);
            }
            if (dateOfBirth !== undefined) {
                updates.push('date_of_birth = ?');
                params.push(dateOfBirth || null);
            }
            if (languagePreference !== undefined) {
                updates.push('language_preference = ?');
                params.push(languagePreference);
            }
            if (timezone !== undefined) {
                updates.push('timezone = ?');
                params.push(timezone);
            }

            if (updates.length === 0) {
                throw new Error('No valid fields to update');
            }

            updates.push('updated_at = datetime("now")');
            params.push(userId);

            await db.run(`
                UPDATE users 
                SET ${updates.join(', ')}
                WHERE id = ?
            `, params);

            // Log audit event
            await authService.logAuditEvent(userId, 'user_info_updated', 'users', userId, 
                { firstName: currentUser.first_name, lastName: currentUser.last_name, phone: currentUser.phone },
                { firstName, lastName, phone, dateOfBirth, languagePreference, timezone }
            );

            return { success: true, message: 'Profile updated successfully' };

        } catch (error) {
            console.error('Error updating user info:', error);
            throw error;
        }
    }

    /**
     * Update user profile (extended information)
     */
    async updateUserProfile(userId, profileData) {
        try {
            const { 
                bio, avatarUrl, website, location, twitterHandle, 
                linkedinUrl, githubUsername, profileVisibility, 
                showEmail, showPhone 
            } = profileData;

            // Validate input
            if (bio && bio.length > 500) {
                throw new Error('Bio must be 500 characters or less');
            }

            if (website && !validator.isURL(website)) {
                throw new Error('Please provide a valid website URL');
            }

            if (linkedinUrl && !validator.isURL(linkedinUrl)) {
                throw new Error('Please provide a valid LinkedIn URL');
            }

            if (profileVisibility && !['public', 'private', 'friends'].includes(profileVisibility)) {
                throw new Error('Invalid profile visibility setting');
            }

            // Check if profile exists
            const existingProfile = await db.get('SELECT id FROM user_profiles WHERE user_id = ?', [userId]);

            if (existingProfile) {
                // Update existing profile
                const updates = [];
                const params = [];

                if (bio !== undefined) { updates.push('bio = ?'); params.push(bio); }
                if (avatarUrl !== undefined) { updates.push('avatar_url = ?'); params.push(avatarUrl); }
                if (website !== undefined) { updates.push('website = ?'); params.push(website); }
                if (location !== undefined) { updates.push('location = ?'); params.push(location); }
                if (twitterHandle !== undefined) { updates.push('twitter_handle = ?'); params.push(twitterHandle); }
                if (linkedinUrl !== undefined) { updates.push('linkedin_url = ?'); params.push(linkedinUrl); }
                if (githubUsername !== undefined) { updates.push('github_username = ?'); params.push(githubUsername); }
                if (profileVisibility !== undefined) { updates.push('profile_visibility = ?'); params.push(profileVisibility); }
                if (showEmail !== undefined) { updates.push('show_email = ?'); params.push(showEmail ? 1 : 0); }
                if (showPhone !== undefined) { updates.push('show_phone = ?'); params.push(showPhone ? 1 : 0); }

                if (updates.length > 0) {
                    updates.push('updated_at = datetime("now")');
                    params.push(userId);

                    await db.run(`
                        UPDATE user_profiles 
                        SET ${updates.join(', ')}
                        WHERE user_id = ?
                    `, params);
                }
            } else {
                // Create new profile
                await db.run(`
                    INSERT INTO user_profiles (
                        user_id, bio, avatar_url, website, location, twitter_handle,
                        linkedin_url, github_username, profile_visibility, show_email, show_phone
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    userId, bio, avatarUrl, website, location, twitterHandle,
                    linkedinUrl, githubUsername, profileVisibility || 'public',
                    showEmail ? 1 : 0, showPhone ? 1 : 0
                ]);
            }

            // Log audit event
            await authService.logAuditEvent(userId, 'user_profile_updated', 'user_profiles', userId, null, profileData);

            return { success: true, message: 'Profile updated successfully' };

        } catch (error) {
            console.error('Error updating user profile:', error);
            throw error;
        }
    }

    /**
     * Update user notification preferences
     */
    async updateNotificationPreferences(userId, preferences) {
        try {
            const { emailNotifications, smsNotifications, marketingEmails } = preferences;

            const updates = [];
            const params = [];

            if (emailNotifications !== undefined) {
                updates.push('email_notifications = ?');
                params.push(emailNotifications ? 1 : 0);
            }
            if (smsNotifications !== undefined) {
                updates.push('sms_notifications = ?');
                params.push(smsNotifications ? 1 : 0);
            }
            if (marketingEmails !== undefined) {
                updates.push('marketing_emails = ?');
                params.push(marketingEmails ? 1 : 0);
            }

            if (updates.length === 0) {
                throw new Error('No preferences to update');
            }

            updates.push('updated_at = datetime("now")');
            params.push(userId);

            await db.run(`
                UPDATE users 
                SET ${updates.join(', ')}
                WHERE id = ?
            `, params);

            // Log audit event
            await authService.logAuditEvent(userId, 'notification_preferences_updated', 'users', userId, null, preferences);

            return { success: true, message: 'Notification preferences updated successfully' };

        } catch (error) {
            console.error('Error updating notification preferences:', error);
            throw error;
        }
    }

    /**
     * Change user password
     */
    async changePassword(userId, currentPassword, newPassword) {
        try {
            // Validate new password
            authService.validatePassword(newPassword);

            // Get current user
            const user = await db.get('SELECT password_hash FROM users WHERE id = ?', [userId]);
            if (!user) {
                throw new Error('User not found');
            }

            // Verify current password
            const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
            if (!isValidPassword) {
                throw new Error('Current password is incorrect');
            }

            // Hash new password
            const newPasswordHash = await bcrypt.hash(newPassword, this.SALT_ROUNDS);

            // Update password and invalidate all sessions except current one
            await db.transaction([
                {
                    sql: 'UPDATE users SET password_hash = ?, password_changed_at = datetime("now") WHERE id = ?',
                    params: [newPasswordHash, userId]
                },
                {
                    sql: 'UPDATE user_sessions SET is_active = 0 WHERE user_id = ?',
                    params: [userId]
                }
            ]);

            // Log audit event
            await authService.logAuditEvent(userId, 'password_changed', 'users', userId);

            return { 
                success: true, 
                message: 'Password changed successfully. Please log in again with your new password.' 
            };

        } catch (error) {
            console.error('Error changing password:', error);
            throw error;
        }
    }

    /**
     * Add user address
     */
    async addAddress(userId, addressData) {
        try {
            const {
                type = 'shipping', label, streetAddress, streetAddress2,
                city, stateProvince, postalCode, country = 'US', isDefault = false
            } = addressData;

            // Validate required fields
            if (!streetAddress || !city || !stateProvince || !postalCode) {
                throw new Error('Street address, city, state/province, and postal code are required');
            }

            if (!['shipping', 'billing', 'home', 'work'].includes(type)) {
                throw new Error('Invalid address type');
            }

            // If this is set as default, unset other defaults of the same type
            if (isDefault) {
                await db.run(`
                    UPDATE user_addresses
                    SET is_default = 0
                    WHERE user_id = ? AND type = ?
                `, [userId, type]);
            }

            const result = await db.run(`
                INSERT INTO user_addresses (
                    user_id, type, label, street_address, street_address_2,
                    city, state_province, postal_code, country, is_default
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                userId, type, label, streetAddress, streetAddress2,
                city, stateProvince, postalCode, country, isDefault ? 1 : 0
            ]);

            // Log audit event
            await authService.logAuditEvent(userId, 'address_added', 'user_addresses', result.id, null, addressData);

            return {
                success: true,
                message: 'Address added successfully',
                addressId: result.id
            };

        } catch (error) {
            console.error('Error adding address:', error);
            throw error;
        }
    }

    /**
     * Update user address
     */
    async updateAddress(userId, addressId, addressData) {
        try {
            // Check if address belongs to user
            const existingAddress = await db.get(`
                SELECT * FROM user_addresses
                WHERE id = ? AND user_id = ?
            `, [addressId, userId]);

            if (!existingAddress) {
                throw new Error('Address not found');
            }

            const {
                type, label, streetAddress, streetAddress2,
                city, stateProvince, postalCode, country, isDefault
            } = addressData;

            // Build update query
            const updates = [];
            const params = [];

            if (type !== undefined) { updates.push('type = ?'); params.push(type); }
            if (label !== undefined) { updates.push('label = ?'); params.push(label); }
            if (streetAddress !== undefined) { updates.push('street_address = ?'); params.push(streetAddress); }
            if (streetAddress2 !== undefined) { updates.push('street_address_2 = ?'); params.push(streetAddress2); }
            if (city !== undefined) { updates.push('city = ?'); params.push(city); }
            if (stateProvince !== undefined) { updates.push('state_province = ?'); params.push(stateProvince); }
            if (postalCode !== undefined) { updates.push('postal_code = ?'); params.push(postalCode); }
            if (country !== undefined) { updates.push('country = ?'); params.push(country); }
            if (isDefault !== undefined) { updates.push('is_default = ?'); params.push(isDefault ? 1 : 0); }

            if (updates.length === 0) {
                throw new Error('No fields to update');
            }

            // If setting as default, unset other defaults of the same type
            if (isDefault) {
                await db.run(`
                    UPDATE user_addresses
                    SET is_default = 0
                    WHERE user_id = ? AND type = ? AND id != ?
                `, [userId, type || existingAddress.type, addressId]);
            }

            updates.push('updated_at = datetime("now")');
            params.push(addressId, userId);

            await db.run(`
                UPDATE user_addresses
                SET ${updates.join(', ')}
                WHERE id = ? AND user_id = ?
            `, params);

            // Log audit event
            await authService.logAuditEvent(userId, 'address_updated', 'user_addresses', addressId, existingAddress, addressData);

            return { success: true, message: 'Address updated successfully' };

        } catch (error) {
            console.error('Error updating address:', error);
            throw error;
        }
    }

    /**
     * Delete user address
     */
    async deleteAddress(userId, addressId) {
        try {
            // Check if address belongs to user
            const existingAddress = await db.get(`
                SELECT * FROM user_addresses
                WHERE id = ? AND user_id = ?
            `, [addressId, userId]);

            if (!existingAddress) {
                throw new Error('Address not found');
            }

            await db.run(`
                UPDATE user_addresses
                SET is_active = 0, updated_at = datetime('now')
                WHERE id = ? AND user_id = ?
            `, [addressId, userId]);

            // Log audit event
            await authService.logAuditEvent(userId, 'address_deleted', 'user_addresses', addressId, existingAddress, null);

            return { success: true, message: 'Address deleted successfully' };

        } catch (error) {
            console.error('Error deleting address:', error);
            throw error;
        }
    }

    /**
     * Get user activity log
     */
    async getUserActivity(userId, limit = 50) {
        try {
            const activities = await db.all(`
                SELECT action, table_name, record_id, new_values, created_at
                FROM audit_log
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT ?
            `, [userId, limit]);

            return activities.map(activity => ({
                ...activity,
                newValues: activity.new_values ? JSON.parse(activity.new_values) : null
            }));

        } catch (error) {
            console.error('Error getting user activity:', error);
            throw error;
        }
    }

    /**
     * Deactivate user account
     */
    async deactivateAccount(userId, reason = null) {
        try {
            await db.transaction([
                {
                    sql: 'UPDATE users SET is_active = 0, updated_at = datetime("now") WHERE id = ?',
                    params: [userId]
                },
                {
                    sql: 'UPDATE user_sessions SET is_active = 0 WHERE user_id = ?',
                    params: [userId]
                }
            ]);

            // Log audit event
            await authService.logAuditEvent(userId, 'account_deactivated', 'users', userId, null, { reason });

            return { success: true, message: 'Account deactivated successfully' };

        } catch (error) {
            console.error('Error deactivating account:', error);
            throw error;
        }
    }

    /**
     * Delete user account permanently
     */
    async deleteAccount(userId) {
        try {
            // This will cascade delete related records due to foreign key constraints
            await db.run('DELETE FROM users WHERE id = ?', [userId]);

            return { success: true, message: 'Account deleted permanently' };

        } catch (error) {
            console.error('Error deleting account:', error);
            throw error;
        }
    }

    /**
     * Get user statistics
     */
    async getUserStats(userId) {
        try {
            const stats = {};

            // Account age
            const user = await db.get('SELECT created_at FROM users WHERE id = ?', [userId]);
            if (user) {
                const accountAge = Math.floor((new Date() - new Date(user.created_at)) / (1000 * 60 * 60 * 24));
                stats.accountAgeDays = accountAge;
            }

            // Login count
            const loginCount = await db.get(`
                SELECT COUNT(*) as count
                FROM login_attempts
                WHERE user_id = ? AND success = 1
            `, [userId]);
            stats.totalLogins = loginCount.count;

            // Active sessions
            const activeSessions = await db.get(`
                SELECT COUNT(*) as count
                FROM user_sessions
                WHERE user_id = ? AND is_active = 1 AND expires_at > datetime('now')
            `, [userId]);
            stats.activeSessions = activeSessions.count;

            // Address count
            const addressCount = await db.get(`
                SELECT COUNT(*) as count
                FROM user_addresses
                WHERE user_id = ? AND is_active = 1
            `, [userId]);
            stats.savedAddresses = addressCount.count;

            return stats;

        } catch (error) {
            console.error('Error getting user stats:', error);
            throw error;
        }
    }
}

module.exports = new UserService();
