/* Authentication Pages Styles */

/* Auth Body Layout */
.auth-body {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.auth-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    min-height: calc(100vh - 200px);
}

.auth-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1200px;
    width: 100%;
    align-items: start;
}

/* Auth Card */
.auth-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: var(--transition);
}

.auth-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
}

/* Auth Header */
.auth-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.auth-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), #00cc7a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    margin: 0;
}

/* Form Styles */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.9rem;
}

.form-input {
    padding: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    font-size: 1rem;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.form-input::placeholder {
    color: var(--text-light);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(0, 255, 153, 0.1);
}

.form-input:invalid {
    border-color: #ff4757;
}

.form-input:valid {
    border-color: var(--primary-color);
}

/* Password Input */
.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: var(--transition);
}

.password-toggle:hover {
    color: var(--primary-color);
    background: rgba(255, 255, 255, 0.05);
}

.password-toggle-icon {
    font-size: 1.2rem;
}

/* Password Requirements */
.password-requirements {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.password-requirement {
    font-size: 0.8rem;
    color: var(--text-light);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    transition: var(--transition);
    margin: 0;
}

.password-requirement.valid {
    color: var(--primary-color);
    background: rgba(0, 255, 153, 0.1);
}

.password-requirement.invalid {
    color: #ff4757;
    background: rgba(255, 71, 87, 0.1);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

/* Checkbox Styles */
.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    user-select: none;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkbox-checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.05);
    position: relative;
    transition: var(--transition);
}

.checkbox-container input[type="checkbox"]:checked + .checkbox-checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked + .checkbox-checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #1a1a1a;
    font-weight: bold;
    font-size: 0.8rem;
}

.checkbox-label {
    color: var(--text-light);
    font-size: 0.9rem;
}

.terms-link {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.terms-link:hover {
    text-decoration: underline;
}

.forgot-password-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password-link:hover {
    text-decoration: underline;
}

/* Button Styles */
.btn-full {
    width: 100%;
    padding: 1.2rem;
    font-size: 1.1rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error and Success Messages */
.form-error {
    color: #ff4757;
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: none;
}

.form-error.show {
    display: block;
}

.form-success {
    color: var(--primary-color);
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: none;
}

.form-success.show {
    display: block;
}

/* Social Login */
.social-login {
    margin: 2rem 0;
}

.social-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.social-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
}

.social-divider-text {
    background: #1a1a1a;
    padding: 0 1rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

.social-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.btn-social {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
}

.btn-social:hover {
    border-color: var(--primary-color);
    background: rgba(0, 255, 153, 0.1);
    transform: translateY(-2px);
}

.social-icon {
    font-size: 1.2rem;
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-footer-text {
    color: var(--text-light);
    margin: 0;
}

.auth-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.auth-link:hover {
    text-decoration: underline;
}

/* Features Section */
.auth-features {
    padding: 2rem 0;
}

.features-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 2rem;
    text-align: center;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: var(--transition);
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 255, 153, 0.2);
    transform: translateX(10px);
}

.feature-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 255, 153, 0.1);
    border-radius: 12px;
    flex-shrink: 0;
}

.feature-content {
    flex: 1;
}

.feature-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.5rem 0;
}

.feature-description {
    color: var(--text-light);
    margin: 0;
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .auth-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        max-width: 600px;
    }
    
    .auth-features {
        order: -1;
    }
}

@media (max-width: 768px) {
    .auth-main {
        padding: 1rem;
    }
    
    .auth-card {
        padding: 2rem;
    }
    
    .auth-title {
        font-size: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .social-buttons {
        grid-template-columns: 1fr;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .auth-card {
        padding: 1.5rem;
        border-radius: 12px;
    }
    
    .auth-title {
        font-size: 1.8rem;
    }
    
    .features-list {
        gap: 1rem;
    }
    
    .feature-item {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }
    
    .feature-icon {
        align-self: center;
    }
}

/* Enhanced Login Styles */

/* Password Strength Indicator */
.password-strength {
    margin-top: 0.5rem;
}

.password-strength-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.password-strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.password-strength-fill.weak {
    background: #ff4757;
    width: 25%;
}

.password-strength-fill.medium {
    background: #ffa502;
    width: 50%;
}

.password-strength-fill.good {
    background: #3742fa;
    width: 75%;
}

.password-strength-fill.strong {
    background: #2ed573;
    width: 100%;
}

.password-strength-text {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 0.25rem;
}

/* Form Row for Side-by-Side Fields */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-row .form-group {
    margin-bottom: 0;
}

/* Enhanced Loading States */
.btn-loading {
    display: none;
    align-items: center;
    gap: 0.5rem;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form Transitions */
.auth-form {
    transition: all 0.3s ease;
}

.auth-title, .auth-subtitle {
    transition: all 0.3s ease;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    margin: 10% auto;
    padding: 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 600;
}

.modal-close {
    color: rgba(255, 255, 255, 0.6);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
    line-height: 1;
    padding: 0.25rem;
    border-radius: 50%;
}

.modal-close:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 2rem;
    text-align: center;
}

.modal-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
}

.modal-icon.success {
    color: #2ed573;
}

.modal-icon.error {
    color: #ff4757;
}

.modal-body p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0;
}

.modal-footer {
    padding: 1rem 2rem 2rem 2rem;
    text-align: center;
}

.modal-footer .btn {
    min-width: 120px;
    padding: 0.75rem 2rem;
}

.btn-success {
    background: linear-gradient(135deg, #2ed573 0%, #17a2b8 100%);
    border: none;
    color: #ffffff;
}

.btn-success:hover {
    background: linear-gradient(135deg, #17a2b8 0%, #2ed573 100%);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    border: none;
    color: #ffffff;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff3742 0%, #ff4757 100%);
    transform: translateY(-2px);
}
