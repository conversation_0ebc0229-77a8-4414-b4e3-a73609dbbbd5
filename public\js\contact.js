// Contact Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize contact page
    initializeContactPage();
    
    // Contact form submission
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }

    // FAQ toggle functionality
    const faqQuestions = document.querySelectorAll('.faq-question');
    faqQuestions.forEach(question => {
        question.addEventListener('click', () => toggleFAQ(question));
    });

    // Auto-fill subject from URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const subject = urlParams.get('subject');
    if (subject) {
        const subjectSelect = document.getElementById('subject');
        if (subjectSelect) {
            // Try to match the subject or set to 'other'
            const option = Array.from(subjectSelect.options).find(opt => 
                opt.value.toLowerCase().includes(subject.toLowerCase()) ||
                opt.text.toLowerCase().includes(subject.toLowerCase())
            );
            if (option) {
                subjectSelect.value = option.value;
            } else {
                subjectSelect.value = 'other';
            }
        }
    }
});

function initializeContactPage() {
    // Animate contact cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe contact cards and FAQ items
    const animateElements = document.querySelectorAll('.contact-card, .faq-item, .social-link-large');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

function handleContactForm(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const contactData = {
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        subject: formData.get('subject'),
        message: formData.get('message'),
        newsletter: formData.get('newsletter') === 'on'
    };
    
    // Validate required fields
    if (!contactData.firstName || !contactData.lastName || !contactData.email || 
        !contactData.subject || !contactData.message) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }
    
    // Validate email
    if (!validateEmail(contactData.email)) {
        showNotification('Please enter a valid email address', 'error');
        return;
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Reset button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
        
        // Show success message
        showNotification('Thank you for your message! We\'ll get back to you within 2 hours.', 'success');
        
        // Reset form
        e.target.reset();
        
        // Track contact form submission
        trackContactSubmission(contactData.subject);
        
    }, 2000);
}

function toggleFAQ(questionElement) {
    const faqItem = questionElement.parentElement;
    const isActive = faqItem.classList.contains('active');
    
    // Close all FAQ items
    document.querySelectorAll('.faq-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // Open clicked item if it wasn't already active
    if (!isActive) {
        faqItem.classList.add('active');
    }
}

function openLiveChat() {
    // Create and show live chat modal
    const modal = createChatModal();
    document.body.appendChild(modal);
    modal.style.display = 'block';
    
    // Focus on first input
    setTimeout(() => {
        const firstInput = modal.querySelector('input');
        if (firstInput) {
            firstInput.focus();
        }
    }, 100);
}

function createChatModal() {
    const modal = document.createElement('div');
    modal.className = 'chat-modal';
    modal.innerHTML = `
        <div class="chat-modal-content">
            <span class="chat-close" onclick="closeChatModal()">&times;</span>
            <div class="chat-header">
                <h2 class="chat-title">Start Live Chat</h2>
                <p class="chat-subtitle">We'll connect you with a support agent</p>
            </div>
            <form class="chat-form" onsubmit="startChat(event)">
                <input type="text" placeholder="Your Name" required>
                <input type="email" placeholder="Your Email" required>
                <textarea placeholder="How can we help you?" rows="3" required></textarea>
                <button type="submit" class="btn btn-primary">Start Chat</button>
            </form>
        </div>
    `;
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeChatModal();
        }
    });
    
    return modal;
}

function closeChatModal() {
    const modal = document.querySelector('.chat-modal');
    if (modal) {
        modal.remove();
    }
}

function startChat(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const chatData = {
        name: formData.get('name') || e.target.querySelector('input[type="text"]').value,
        email: formData.get('email') || e.target.querySelector('input[type="email"]').value,
        message: formData.get('message') || e.target.querySelector('textarea').value
    };
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.textContent = 'Connecting...';
    submitBtn.disabled = true;
    
    // Simulate chat connection
    setTimeout(() => {
        closeChatModal();
        showNotification('Chat feature coming soon! Please use our contact form or call us directly.', 'info');
        
        // Track chat attempt
        trackChatAttempt();
    }, 1500);
}

function showMap() {
    // Create and show map modal
    const modal = createMapModal();
    document.body.appendChild(modal);
    modal.style.display = 'block';
}

function createMapModal() {
    const modal = document.createElement('div');
    modal.className = 'map-modal';
    modal.innerHTML = `
        <div class="map-modal-content">
            <span class="map-close" onclick="closeMapModal()">&times;</span>
            <div class="map-placeholder">
                <h3>Sports Malta HQ</h3>
                <p>Valletta, Malta</p>
                <p style="margin-top: 1rem; font-style: italic;">Interactive map coming soon!</p>
                <div style="margin-top: 2rem;">
                    <p><strong>Address:</strong> Sports Malta Headquarters<br>
                    Valletta, Malta</p>
                    <p><strong>Hours:</strong> Monday-Friday: 9AM-6PM</p>
                    <p><strong>Phone:</strong> +356 1234 5678</p>
                </div>
            </div>
        </div>
    `;
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeMapModal();
        }
    });
    
    return modal;
}

function closeMapModal() {
    const modal = document.querySelector('.map-modal');
    if (modal) {
        modal.remove();
    }
}

// Analytics and tracking functions
function trackContactSubmission(subject) {
    console.log('Contact Analytics:', {
        action: 'form_submission',
        subject: subject,
        timestamp: new Date().toISOString(),
        page: 'contact'
    });
}

function trackChatAttempt() {
    console.log('Contact Analytics:', {
        action: 'chat_attempt',
        timestamp: new Date().toISOString(),
        page: 'contact'
    });
}

function trackFAQInteraction(question) {
    console.log('Contact Analytics:', {
        action: 'faq_click',
        question: question,
        timestamp: new Date().toISOString(),
        page: 'contact'
    });
}

// Enhanced FAQ functionality
function searchFAQ(query) {
    const faqItems = document.querySelectorAll('.faq-item');
    const searchTerm = query.toLowerCase();
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question h3').textContent.toLowerCase();
        const answer = item.querySelector('.faq-answer p').textContent.toLowerCase();
        
        if (question.includes(searchTerm) || answer.includes(searchTerm)) {
            item.style.display = 'block';
            
            // Highlight matching text
            if (question.includes(searchTerm)) {
                item.classList.add('highlighted');
            }
        } else {
            item.style.display = 'none';
        }
    });
}

// Contact method tracking
function trackContactMethod(method) {
    console.log('Contact Analytics:', {
        action: 'contact_method_used',
        method: method,
        timestamp: new Date().toISOString(),
        page: 'contact'
    });
}

// Add click tracking to contact methods
document.addEventListener('DOMContentLoaded', function() {
    // Track phone calls
    const phoneLinks = document.querySelectorAll('a[href^="tel:"]');
    phoneLinks.forEach(link => {
        link.addEventListener('click', () => trackContactMethod('phone'));
    });
    
    // Track email links
    const emailLinks = document.querySelectorAll('a[href^="mailto:"]');
    emailLinks.forEach(link => {
        link.addEventListener('click', () => trackContactMethod('email'));
    });
    
    // Track social media clicks
    const socialLinks = document.querySelectorAll('.social-link-large');
    socialLinks.forEach(link => {
        link.addEventListener('click', function() {
            const platform = this.classList.contains('instagram') ? 'instagram' :
                           this.classList.contains('facebook') ? 'facebook' :
                           this.classList.contains('twitter') ? 'twitter' : 'unknown';
            trackContactMethod(`social_${platform}`);
        });
    });
});

// Auto-expand FAQ based on URL hash
window.addEventListener('load', function() {
    const hash = window.location.hash;
    if (hash && hash.startsWith('#faq-')) {
        const faqIndex = parseInt(hash.replace('#faq-', '')) - 1;
        const faqItems = document.querySelectorAll('.faq-item');
        if (faqItems[faqIndex]) {
            faqItems[faqIndex].classList.add('active');
            faqItems[faqIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
});

// Keyboard navigation for FAQ
document.addEventListener('keydown', function(e) {
    if (e.target.closest('.faq-question') && (e.key === 'Enter' || e.key === ' ')) {
        e.preventDefault();
        toggleFAQ(e.target.closest('.faq-question'));
    }
});

// Form validation enhancements
function validateContactForm(formData) {
    const errors = [];
    
    // Name validation
    if (!formData.firstName.trim() || formData.firstName.length < 2) {
        errors.push('First name must be at least 2 characters long');
    }
    
    if (!formData.lastName.trim() || formData.lastName.length < 2) {
        errors.push('Last name must be at least 2 characters long');
    }
    
    // Email validation
    if (!validateEmail(formData.email)) {
        errors.push('Please enter a valid email address');
    }
    
    // Phone validation (if provided)
    if (formData.phone && !validatePhone(formData.phone)) {
        errors.push('Please enter a valid phone number');
    }
    
    // Message validation
    if (!formData.message.trim() || formData.message.length < 10) {
        errors.push('Message must be at least 10 characters long');
    }
    
    return errors;
}

function validatePhone(phone) {
    // Basic phone validation for Malta numbers
    const phoneRegex = /^(\+356|356)?\s?[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
}

// Export functions for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        handleContactForm,
        toggleFAQ,
        validateContactForm,
        validatePhone,
        trackContactSubmission
    };
}