/**
 * Authentication Service
 * Handles user registration, login, password management, and security
 */

const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const validator = require('validator');
const { db } = require('../database/database');

class AuthService {
    constructor() {
        this.JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
        this.SALT_ROUNDS = 12;
        this.MAX_LOGIN_ATTEMPTS = 5;
        this.LOCKOUT_DURATION = 15; // minutes
        this.TOKEN_EXPIRY = {
            access: '24h',
            refresh: '30d',
            remember: '30d',
            verification: '24h',
            reset: '1h'
        };
    }

    /**
     * Register a new user
     */
    async register(userData) {
        try {
            const { email, password, firstName, lastName, phone } = userData;

            // Validate input
            this.validateRegistrationData(userData);

            // Check if user already exists
            const existingUser = await db.get(
                'SELECT id, email FROM users WHERE email = ?',
                [email.toLowerCase()]
            );

            if (existingUser) {
                throw new Error('An account with this email already exists');
            }

            // Hash password
            const passwordHash = await bcrypt.hash(password, this.SALT_ROUNDS);

            // Create user
            const result = await db.run(`
                INSERT INTO users (
                    email, password_hash, first_name, last_name, phone,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            `, [
                email.toLowerCase(),
                passwordHash,
                firstName.trim(),
                lastName.trim(),
                phone || null
            ]);

            const userId = result.id;

            // Create user profile
            await db.run(`
                INSERT INTO user_profiles (user_id, created_at, updated_at)
                VALUES (?, datetime('now'), datetime('now'))
            `, [userId]);

            // Generate email verification token
            const verificationToken = await this.generateEmailVerificationToken(userId, email);

            // Log registration
            await this.logAuditEvent(userId, 'user_registered', 'users', userId, null, {
                email: email.toLowerCase(),
                firstName,
                lastName
            });

            return {
                success: true,
                userId,
                message: 'Account created successfully! Please check your email to verify your account.',
                verificationToken // In production, send this via email
            };

        } catch (error) {
            console.error('Registration error:', error);
            throw error;
        }
    }

    /**
     * Authenticate user login
     */
    async login(email, password, options = {}) {
        try {
            const { rememberMe = false, ipAddress, userAgent } = options;

            // Validate input
            if (!email || !password) {
                await this.logLoginAttempt(email, false, 'Missing credentials', ipAddress, userAgent);
                throw new Error('Email and password are required');
            }

            if (!validator.isEmail(email)) {
                await this.logLoginAttempt(email, false, 'Invalid email format', ipAddress, userAgent);
                throw new Error('Please provide a valid email address');
            }

            // Get user
            const user = await db.get(`
                SELECT id, email, password_hash, first_name, last_name, 
                       is_active, is_verified, failed_login_attempts, locked_until
                FROM users 
                WHERE email = ?
            `, [email.toLowerCase()]);

            if (!user) {
                await this.logLoginAttempt(email, false, 'User not found', ipAddress, userAgent);
                throw new Error('Invalid email or password');
            }

            // Check if account is locked
            if (user.locked_until && new Date(user.locked_until) > new Date()) {
                await this.logLoginAttempt(email, false, 'Account locked', ipAddress, userAgent, user.id);
                throw new Error('Account is temporarily locked due to too many failed login attempts');
            }

            // Check if account is active
            if (!user.is_active) {
                await this.logLoginAttempt(email, false, 'Account deactivated', ipAddress, userAgent, user.id);
                throw new Error('Account is deactivated');
            }

            // Verify password
            const isValidPassword = await bcrypt.compare(password, user.password_hash);
            if (!isValidPassword) {
                await this.handleFailedLogin(user.id, email, ipAddress, userAgent);
                throw new Error('Invalid email or password');
            }

            // Reset failed login attempts on successful login
            await db.run(`
                UPDATE users 
                SET failed_login_attempts = 0, locked_until = NULL, last_login_at = datetime('now')
                WHERE id = ?
            `, [user.id]);

            // Generate JWT token
            const tokenExpiry = rememberMe ? this.TOKEN_EXPIRY.remember : this.TOKEN_EXPIRY.access;
            const token = this.generateJWT({
                userId: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name
            }, tokenExpiry);

            // Store session
            await this.createSession(user.id, token, {
                rememberMe,
                ipAddress,
                userAgent,
                expiresIn: tokenExpiry
            });

            // Log successful login
            await this.logLoginAttempt(email, true, null, ipAddress, userAgent, user.id);
            await this.logAuditEvent(user.id, 'user_login', null, null, null, {
                ipAddress,
                userAgent,
                rememberMe
            });

            return {
                success: true,
                token,
                user: {
                    id: user.id,
                    email: user.email,
                    firstName: user.first_name,
                    lastName: user.last_name,
                    isVerified: user.is_verified
                }
            };

        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    /**
     * Verify JWT token and get user info
     */
    async verifyToken(token) {
        try {
            // Verify JWT
            const decoded = jwt.verify(token, this.JWT_SECRET);

            // Check if session exists and is active
            const session = await db.get(`
                SELECT s.*, u.is_active, u.is_verified
                FROM user_sessions s
                JOIN users u ON s.user_id = u.id
                WHERE s.token_hash = ? AND s.is_active = 1 AND s.expires_at > datetime('now')
            `, [db.hashToken(token)]);

            if (!session) {
                throw new Error('Invalid or expired session');
            }

            if (!session.is_active) {
                throw new Error('User account is deactivated');
            }

            // Update last used timestamp
            await db.run(`
                UPDATE user_sessions 
                SET last_used_at = datetime('now')
                WHERE id = ?
            `, [session.id]);

            return {
                valid: true,
                user: {
                    id: decoded.userId,
                    email: decoded.email,
                    firstName: decoded.firstName,
                    lastName: decoded.lastName,
                    isVerified: session.is_verified
                }
            };

        } catch (error) {
            return {
                valid: false,
                error: error.message
            };
        }
    }

    /**
     * Logout user (invalidate session)
     */
    async logout(token) {
        try {
            const tokenHash = db.hashToken(token);
            
            await db.run(`
                UPDATE user_sessions 
                SET is_active = 0 
                WHERE token_hash = ?
            `, [tokenHash]);

            return { success: true, message: 'Logged out successfully' };

        } catch (error) {
            console.error('Logout error:', error);
            throw error;
        }
    }

    /**
     * Generate email verification token
     */
    async generateEmailVerificationToken(userId, email) {
        try {
            const token = db.generateToken(32);
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + 24); // 24 hours

            await db.run(`
                INSERT INTO email_verifications (user_id, email, token, expires_at)
                VALUES (?, ?, ?, ?)
            `, [userId, email, token, expiresAt.toISOString()]);

            return token;

        } catch (error) {
            console.error('Error generating verification token:', error);
            throw error;
        }
    }

    /**
     * Verify email with token
     */
    async verifyEmail(token) {
        try {
            const verification = await db.get(`
                SELECT * FROM email_verifications 
                WHERE token = ? AND is_used = 0 AND expires_at > datetime('now')
            `, [token]);

            if (!verification) {
                throw new Error('Invalid or expired verification token');
            }

            // Mark email as verified
            await db.transaction([
                {
                    sql: 'UPDATE users SET is_verified = 1 WHERE id = ?',
                    params: [verification.user_id]
                },
                {
                    sql: 'UPDATE email_verifications SET is_used = 1, verified_at = datetime("now") WHERE id = ?',
                    params: [verification.id]
                }
            ]);

            await this.logAuditEvent(verification.user_id, 'email_verified', 'users', verification.user_id);

            return { success: true, message: 'Email verified successfully' };

        } catch (error) {
            console.error('Email verification error:', error);
            throw error;
        }
    }

    /**
     * Generate password reset token
     */
    async generatePasswordResetToken(email, ipAddress, userAgent) {
        try {
            const user = await db.get('SELECT id FROM users WHERE email = ?', [email.toLowerCase()]);
            
            if (!user) {
                // Don't reveal if email exists
                return { success: true, message: 'If an account with this email exists, a password reset link has been sent.' };
            }

            const token = db.generateToken(32);
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + 1); // 1 hour

            await db.run(`
                INSERT INTO password_resets (user_id, token, expires_at, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            `, [user.id, token, expiresAt.toISOString(), ipAddress, userAgent]);

            await this.logAuditEvent(user.id, 'password_reset_requested', null, null, null, { email, ipAddress });

            return {
                success: true,
                message: 'If an account with this email exists, a password reset link has been sent.',
                token // In production, send this via email
            };

        } catch (error) {
            console.error('Password reset token generation error:', error);
            throw error;
        }
    }

    /**
     * Reset password with token
     */
    async resetPassword(token, newPassword) {
        try {
            this.validatePassword(newPassword);

            const reset = await db.get(`
                SELECT * FROM password_resets 
                WHERE token = ? AND is_used = 0 AND expires_at > datetime('now')
            `, [token]);

            if (!reset) {
                throw new Error('Invalid or expired reset token');
            }

            const passwordHash = await bcrypt.hash(newPassword, this.SALT_ROUNDS);

            await db.transaction([
                {
                    sql: 'UPDATE users SET password_hash = ?, password_changed_at = datetime("now") WHERE id = ?',
                    params: [passwordHash, reset.user_id]
                },
                {
                    sql: 'UPDATE password_resets SET is_used = 1, used_at = datetime("now") WHERE id = ?',
                    params: [reset.id]
                },
                {
                    sql: 'UPDATE user_sessions SET is_active = 0 WHERE user_id = ?',
                    params: [reset.user_id]
                }
            ]);

            await this.logAuditEvent(reset.user_id, 'password_reset_completed', 'users', reset.user_id);

            return { success: true, message: 'Password reset successfully' };

        } catch (error) {
            console.error('Password reset error:', error);
            throw error;
        }
    }

    /**
     * Handle failed login attempt
     */
    async handleFailedLogin(userId, email, ipAddress, userAgent) {
        try {
            // Increment failed attempts
            const result = await db.run(`
                UPDATE users
                SET failed_login_attempts = failed_login_attempts + 1
                WHERE id = ?
            `, [userId]);

            // Get updated user data
            const user = await db.get(`
                SELECT failed_login_attempts FROM users WHERE id = ?
            `, [userId]);

            // Lock account if too many attempts
            if (user.failed_login_attempts >= this.MAX_LOGIN_ATTEMPTS) {
                const lockUntil = new Date();
                lockUntil.setMinutes(lockUntil.getMinutes() + this.LOCKOUT_DURATION);

                await db.run(`
                    UPDATE users
                    SET locked_until = ?
                    WHERE id = ?
                `, [lockUntil.toISOString(), userId]);

                await this.logAuditEvent(userId, 'account_locked', 'users', userId, null, {
                    reason: 'too_many_failed_attempts',
                    attempts: user.failed_login_attempts
                });
            }

            await this.logLoginAttempt(email, false, 'Invalid password', ipAddress, userAgent, userId);

        } catch (error) {
            console.error('Error handling failed login:', error);
        }
    }

    /**
     * Create user session
     */
    async createSession(userId, token, options = {}) {
        try {
            const { rememberMe = false, ipAddress, userAgent, expiresIn = '24h' } = options;

            const expiresAt = new Date();
            if (rememberMe || expiresIn === '30d') {
                expiresAt.setDate(expiresAt.getDate() + 30);
            } else {
                expiresAt.setDate(expiresAt.getDate() + 1);
            }

            const tokenHash = db.hashToken(token);

            await db.run(`
                INSERT INTO user_sessions (
                    user_id, token_hash, device_info, ip_address, user_agent,
                    expires_at, remember_me
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
                userId,
                tokenHash,
                userAgent || 'Unknown',
                ipAddress,
                userAgent,
                expiresAt.toISOString(),
                rememberMe ? 1 : 0
            ]);

        } catch (error) {
            console.error('Error creating session:', error);
            throw error;
        }
    }

    /**
     * Generate JWT token
     */
    generateJWT(payload, expiresIn = '24h') {
        return jwt.sign(payload, this.JWT_SECRET, { expiresIn });
    }

    /**
     * Log login attempt
     */
    async logLoginAttempt(email, success, failureReason, ipAddress, userAgent, userId = null) {
        try {
            await db.run(`
                INSERT INTO login_attempts (
                    email, success, failure_reason, ip_address, user_agent, user_id
                ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
                email?.toLowerCase() || 'unknown',
                success ? 1 : 0,
                failureReason,
                ipAddress,
                userAgent,
                userId
            ]);
        } catch (error) {
            console.error('Error logging login attempt:', error);
        }
    }

    /**
     * Log audit event
     */
    async logAuditEvent(userId, action, tableName = null, recordId = null, oldValues = null, newValues = null, ipAddress = null, userAgent = null, sessionId = null) {
        try {
            await db.run(`
                INSERT INTO audit_log (
                    user_id, action, table_name, record_id, old_values, new_values,
                    ip_address, user_agent, session_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                userId,
                action,
                tableName,
                recordId,
                oldValues ? JSON.stringify(oldValues) : null,
                newValues ? JSON.stringify(newValues) : null,
                ipAddress,
                userAgent,
                sessionId
            ]);
        } catch (error) {
            console.error('Error logging audit event:', error);
        }
    }

    /**
     * Validate registration data
     */
    validateRegistrationData(data) {
        const { email, password, firstName, lastName } = data;

        if (!email || !validator.isEmail(email)) {
            throw new Error('Please provide a valid email address');
        }

        if (email.length > 255) {
            throw new Error('Email address is too long');
        }

        this.validatePassword(password);

        if (!firstName || firstName.trim().length < 2 || firstName.trim().length > 50) {
            throw new Error('First name must be between 2 and 50 characters');
        }

        if (!lastName || lastName.trim().length < 2 || lastName.trim().length > 50) {
            throw new Error('Last name must be between 2 and 50 characters');
        }
    }

    /**
     * Validate password
     */
    validatePassword(password) {
        if (!password) {
            throw new Error('Password is required');
        }

        if (password.length < 8) {
            throw new Error('Password must be at least 8 characters long');
        }

        if (password.length > 128) {
            throw new Error('Password is too long');
        }

        // Check for at least one uppercase, one lowercase, one number
        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
            throw new Error('Password must contain at least one uppercase letter, one lowercase letter, and one number');
        }
    }

    /**
     * Get user sessions
     */
    async getUserSessions(userId) {
        try {
            return await db.all(`
                SELECT id, device_info, ip_address, created_at, last_used_at, expires_at, is_active
                FROM user_sessions
                WHERE user_id = ? AND is_active = 1
                ORDER BY last_used_at DESC
            `, [userId]);
        } catch (error) {
            console.error('Error getting user sessions:', error);
            throw error;
        }
    }

    /**
     * Revoke session
     */
    async revokeSession(userId, sessionId) {
        try {
            await db.run(`
                UPDATE user_sessions
                SET is_active = 0
                WHERE id = ? AND user_id = ?
            `, [sessionId, userId]);

            return { success: true, message: 'Session revoked successfully' };
        } catch (error) {
            console.error('Error revoking session:', error);
            throw error;
        }
    }

    /**
     * Revoke all sessions for user
     */
    async revokeAllSessions(userId, exceptSessionId = null) {
        try {
            let sql = 'UPDATE user_sessions SET is_active = 0 WHERE user_id = ?';
            let params = [userId];

            if (exceptSessionId) {
                sql += ' AND id != ?';
                params.push(exceptSessionId);
            }

            await db.run(sql, params);

            return { success: true, message: 'All sessions revoked successfully' };
        } catch (error) {
            console.error('Error revoking all sessions:', error);
            throw error;
        }
    }
}

module.exports = new AuthService();
