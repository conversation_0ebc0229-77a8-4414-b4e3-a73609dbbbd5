<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Sports Malta</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
</head>
<body class="admin-body">
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="admin-header-content">
            <div class="admin-logo">
                <h1>🏆 Sports Malta Admin</h1>
            </div>
            <div class="admin-user">
                <span id="adminUserName">Administrator</span>
                <button id="adminLogout" class="btn btn-outline">Logout</button>
            </div>
        </div>
    </header>

    <!-- Admin Main Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Dashboard Stats -->
            <section class="admin-stats">
                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <h3 id="totalUsers">0</h3>
                        <p>Total Users</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🆕</div>
                    <div class="stat-content">
                        <h3 id="newUsersToday">0</h3>
                        <p>New Today</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-content">
                        <h3 id="totalBookings">0</h3>
                        <p>Total Bookings</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🔄</div>
                    <div class="stat-content">
                        <h3 id="activeUsers">0</h3>
                        <p>Active Users</p>
                    </div>
                </div>
            </section>

            <!-- Users Management -->
            <section class="admin-section">
                <div class="section-header">
                    <h2>User Management</h2>
                    <div class="section-actions">
                        <input type="text" id="userSearch" placeholder="Search users..." class="search-input">
                        <button id="refreshUsers" class="btn btn-primary">Refresh</button>
                        <button id="exportUsers" class="btn btn-outline">Export CSV</button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="admin-table" id="usersTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Joined</th>
                                <th>Last Login</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- Users will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <div class="table-pagination">
                    <div class="pagination-info">
                        <span id="paginationInfo">Showing 0 of 0 users</span>
                    </div>
                    <div class="pagination-controls">
                        <button id="prevPage" class="btn btn-outline" disabled>Previous</button>
                        <span id="pageNumbers" class="page-numbers"></span>
                        <button id="nextPage" class="btn btn-outline" disabled>Next</button>
                    </div>
                </div>
            </section>

            <!-- Recent Activity -->
            <section class="admin-section">
                <div class="section-header">
                    <h2>Recent Activity</h2>
                    <button id="refreshActivity" class="btn btn-outline">Refresh</button>
                </div>

                <div class="activity-list" id="activityList">
                    <!-- Activity items will be loaded here -->
                </div>
            </section>
        </div>
    </main>

    <!-- User Details Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>User Details</h3>
                <span class="modal-close" id="userModalClose">&times;</span>
            </div>
            <div class="modal-body">
                <div id="userDetails">
                    <!-- User details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="deleteUser" class="btn btn-danger">Delete User</button>
                <button id="closeUserModal" class="btn btn-outline">Close</button>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="confirmTitle">Confirm Action</h3>
                <span class="modal-close" id="confirmModalClose">&times;</span>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">Are you sure you want to perform this action?</p>
            </div>
            <div class="modal-footer">
                <button id="confirmAction" class="btn btn-danger">Confirm</button>
                <button id="cancelAction" class="btn btn-outline">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner-large"></div>
        <p>Loading...</p>
    </div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // Initialize admin dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Check admin authentication
            const authToken = localStorage.getItem('authToken');
            const currentUser = localStorage.getItem('currentUser');
            
            if (!authToken || !currentUser) {
                window.location.href = '/login.html?returnUrl=/admin.html';
                return;
            }
            
            // Parse user data
            let user;
            try {
                user = JSON.parse(currentUser);
            } catch (e) {
                window.location.href = '/login.html?returnUrl=/admin.html';
                return;
            }
            
            // Check if user is admin (you can modify this logic)
            const adminEmails = ['<EMAIL>', '<EMAIL>'];
            if (!adminEmails.includes(user.email)) {
                alert('Access denied. Admin privileges required.');
                window.location.href = '/account.html';
                return;
            }
            
            // Initialize admin dashboard
            window.adminDashboard = new AdminDashboard();
        });
    </script>
</body>
</html>
