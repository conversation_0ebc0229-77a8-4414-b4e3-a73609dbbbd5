/**
 * Enhanced Login Manager - Similar to Julia Magnets functionality
 * Handles login, registration, form toggling, and enhanced UX features
 */

class EnhancedLoginManager {
    constructor() {
        this.apiBaseUrl = '/api';
        this.csrfToken = null;
        this.currentForm = 'login'; // 'login' or 'register'
        
        this.initializeEventListeners();
        this.fetchCSRFToken();
    }

    // Initialize all event listeners
    initializeEventListeners() {
        // Login form submission
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Registration form submission
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => this.handleRegister(e));
        }

        // Toggle between login and register forms
        const showRegisterLink = document.getElementById('showRegister');
        const showLoginLink = document.getElementById('showLogin');
        
        if (showRegisterLink) {
            showRegisterLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleForms('register');
            });
        }

        if (showLoginLink) {
            showLoginLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleForms('login');
            });
        }

        // Password visibility toggles
        this.initPasswordToggles();

        // Real-time password strength validation
        const regPassword = document.getElementById('regPassword');
        if (regPassword) {
            regPassword.addEventListener('input', () => this.checkPasswordStrength());
        }

        // Real-time password confirmation validation
        const regConfirmPassword = document.getElementById('regConfirmPassword');
        if (regConfirmPassword) {
            regConfirmPassword.addEventListener('input', () => this.checkPasswordMatch());
        }

        // Social login buttons (placeholder functionality)
        const googleLogin = document.getElementById('googleLogin');
        const facebookLogin = document.getElementById('facebookLogin');
        
        if (googleLogin) {
            googleLogin.addEventListener('click', () => this.handleSocialLogin('google'));
        }
        
        if (facebookLogin) {
            facebookLogin.addEventListener('click', () => this.handleSocialLogin('facebook'));
        }
    }

    // Initialize password visibility toggles
    initPasswordToggles() {
        const toggles = [
            { button: 'passwordToggle', input: 'password' },
            { button: 'regPasswordToggle', input: 'regPassword' },
            { button: 'regConfirmPasswordToggle', input: 'regConfirmPassword' }
        ];

        toggles.forEach(({ button, input }) => {
            const toggleBtn = document.getElementById(button);
            const inputField = document.getElementById(input);
            
            if (toggleBtn && inputField) {
                toggleBtn.addEventListener('click', () => {
                    const isPassword = inputField.type === 'password';
                    inputField.type = isPassword ? 'text' : 'password';
                    toggleBtn.querySelector('.password-toggle-icon').textContent = isPassword ? '🙈' : '👁️';
                });
            }
        });
    }

    // Toggle between login and register forms
    toggleForms(formType) {
        const loginForm = document.getElementById('loginForm');
        const registerForm = document.getElementById('registerForm');
        const authTitle = document.querySelector('.auth-title');
        const authSubtitle = document.querySelector('.auth-subtitle');
        const authFooter = document.querySelector('.auth-footer');
        const backToLogin = document.getElementById('backToLogin');
        const socialLogin = document.querySelector('.social-login');

        if (formType === 'register') {
            // Show registration form
            loginForm.style.display = 'none';
            registerForm.style.display = 'block';
            authFooter.style.display = 'none';
            backToLogin.style.display = 'block';
            socialLogin.style.display = 'none';
            
            // Update header text
            authTitle.textContent = 'Create Account';
            authSubtitle.textContent = 'Join Sports Malta and start booking today';
            
            this.currentForm = 'register';
        } else {
            // Show login form
            loginForm.style.display = 'block';
            registerForm.style.display = 'none';
            authFooter.style.display = 'block';
            backToLogin.style.display = 'none';
            socialLogin.style.display = 'block';
            
            // Update header text
            authTitle.textContent = 'Welcome Back';
            authSubtitle.textContent = 'Sign in to your Sports Malta account';
            
            this.currentForm = 'login';
        }

        // Clear any error messages
        this.clearMessages();
    }

    // Fetch CSRF token
    async fetchCSRFToken() {
        // CSRF token disabled for development
        console.log('📧 CSRF token disabled for development');
        return;
    }

    // Handle login form submission
    async handleLogin(e) {
        e.preventDefault();
        
        const email = document.getElementById('email')?.value.trim();
        const password = document.getElementById('password')?.value;
        const rememberMe = document.getElementById('rememberMe')?.checked;
        const submitBtn = document.getElementById('loginButton');
        
        // Validate inputs
        if (!email || !password) {
            this.showMessage('Please enter both email and password', 'error', 'form-error');
            return;
        }
        
        try {
            // Show loading state
            this.setButtonLoading(submitBtn, true, 'Signing in...');
            
            // Call login API
            const response = await fetch(`${this.apiBaseUrl}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    email,
                    password,
                    rememberMe
                })
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error?.message || 'Invalid email or password. Please try again.');
            }
            
            // Store authentication data
            if (data.token) {
                localStorage.setItem('authToken', data.token);
            }
            if (data.user) {
                localStorage.setItem('currentUser', JSON.stringify(data.user));
            }

            // Dispatch auth state change event to update navigation
            document.dispatchEvent(new CustomEvent('authStateChanged', {
                detail: { user: data.user, token: data.token }
            }));

            // Show success modal and redirect
            this.showModal('success', 'Login Successful!', 'Welcome back! Redirecting to your account...', () => {
                let returnUrl = new URLSearchParams(window.location.search).get('returnUrl') || '/public/account.html';
                // Clean up the return URL to use absolute paths
                if (returnUrl === 'account.html' || returnUrl === '/account.html') {
                    returnUrl = '/public/account.html';
                }
                window.location.href = returnUrl;
            });

        } catch (error) {
            this.showModal('error', 'Login Failed', error.message);
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    // Handle registration form submission
    async handleRegister(e) {
        e.preventDefault();
        
        const formData = {
            firstName: document.getElementById('regFirstName')?.value.trim(),
            lastName: document.getElementById('regLastName')?.value.trim(),
            email: document.getElementById('regEmail')?.value.trim(),
            phone: document.getElementById('regPhone')?.value.trim(),
            password: document.getElementById('regPassword')?.value,
            confirmPassword: document.getElementById('regConfirmPassword')?.value
        };
        
        const submitBtn = document.getElementById('registerButton');

        // Validate inputs
        if (!this.validateRegistrationForm(formData)) {
            return;
        }
        
        try {
            // Show loading state
            this.setButtonLoading(submitBtn, true, 'Creating account...');
            
            // Call signup API
            const response = await fetch(`${this.apiBaseUrl}/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    name: `${formData.firstName} ${formData.lastName}`,
                    email: formData.email,
                    password: formData.password,
                    confirmPassword: formData.confirmPassword
                })
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error?.message || 'Registration failed. Please try again.');
            }

            // Show success modal and redirect to login
            this.showModal('success', 'Account Created!', 'Your account has been created successfully. Please log in to continue.', () => {
                this.toggleForms('login');
                // Clear the registration form
                document.getElementById('registerForm').reset();
            });

        } catch (error) {
            this.showModal('error', 'Registration Failed', error.message);
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    // Validate registration form
    validateRegistrationForm(formData) {
        let isValid = true;

        // Clear previous errors
        this.clearFieldErrors();

        // Validate required fields
        if (!formData.firstName) {
            this.showFieldError('regFirstName', 'First name is required');
            isValid = false;
        }

        if (!formData.lastName) {
            this.showFieldError('regLastName', 'Last name is required');
            isValid = false;
        }

        if (!formData.email) {
            this.showFieldError('regEmail', 'Email is required');
            isValid = false;
        } else if (!this.isValidEmail(formData.email)) {
            this.showFieldError('regEmail', 'Please enter a valid email address');
            isValid = false;
        }

        if (!formData.phone) {
            this.showFieldError('regPhone', 'Phone number is required');
            isValid = false;
        }

        if (!formData.password) {
            this.showFieldError('regPassword', 'Password is required');
            isValid = false;
        }

        if (formData.password !== formData.confirmPassword) {
            this.showFieldError('regConfirmPassword', 'Passwords do not match');
            isValid = false;
        }

        return isValid;
    }

    // Check password strength in real-time
    checkPasswordStrength() {
        const password = document.getElementById('regPassword')?.value || '';
        const strengthDiv = document.getElementById('regPasswordStrength');

        if (!strengthDiv) return;

        const strength = this.calculatePasswordStrength(password);

        strengthDiv.innerHTML = `
            <div class="password-strength-bar">
                <div class="password-strength-fill ${strength.class}" style="width: ${strength.percentage}%"></div>
            </div>
            <div class="password-strength-text">${strength.text}</div>
        `;
    }

    // Check password confirmation match
    checkPasswordMatch() {
        const password = document.getElementById('regPassword')?.value || '';
        const confirmPassword = document.getElementById('regConfirmPassword')?.value || '';
        const errorDiv = document.getElementById('regConfirmPassword-error');

        if (!errorDiv) return;

        if (confirmPassword && password !== confirmPassword) {
            errorDiv.textContent = 'Passwords do not match';
            errorDiv.style.display = 'block';
        } else {
            errorDiv.textContent = '';
            errorDiv.style.display = 'none';
        }
    }

    // Calculate password strength
    calculatePasswordStrength(password) {
        // No restrictions - any password is valid
        if (password.length === 0) {
            return {
                percentage: 0,
                class: 'weak',
                text: 'Enter a password'
            };
        }

        return {
            percentage: 100,
            class: 'strong',
            text: 'Password accepted'
        };
    }

    // Handle social login (placeholder)
    handleSocialLogin(provider) {
        this.showMessage(`${provider.charAt(0).toUpperCase() + provider.slice(1)} login is coming soon!`, 'info', 'form-error');
    }

    // Utility functions
    setButtonLoading(button, isLoading, loadingText = 'Loading...') {
        if (!button) return;

        const btnText = button.querySelector('.btn-text');
        const btnLoading = button.querySelector('.btn-loading');

        if (isLoading) {
            button.disabled = true;
            if (btnText) btnText.style.display = 'none';
            if (btnLoading) {
                btnLoading.style.display = 'inline-flex';
                if (loadingText) {
                    const loadingTextSpan = btnLoading.querySelector('span:last-child');
                    if (loadingTextSpan) loadingTextSpan.textContent = loadingText;
                }
            }
        } else {
            button.disabled = false;
            if (btnText) btnText.style.display = 'inline';
            if (btnLoading) btnLoading.style.display = 'none';
        }
    }

    showMessage(message, type, elementId) {
        const element = document.getElementById(elementId);
        if (!element) return;

        element.textContent = message;
        element.className = `form-${type}`;
        element.style.display = 'block';

        // Auto-hide success messages
        if (type === 'success') {
            setTimeout(() => {
                element.style.display = 'none';
            }, 5000);
        }
    }

    showFieldError(fieldId, message) {
        const errorElement = document.getElementById(`${fieldId}-error`);
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    clearMessages() {
        const messageElements = [
            'form-error', 'form-success',
            'register-form-error', 'register-form-success'
        ];

        messageElements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = 'none';
                element.textContent = '';
            }
        });
    }

    clearFieldErrors() {
        const errorElements = document.querySelectorAll('.form-error');
        errorElements.forEach(element => {
            element.style.display = 'none';
            element.textContent = '';
        });
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    isStrongPassword(password) {
        // No restrictions - any non-empty password is valid
        return password && password.length > 0;
    }

    // Modal functionality
    showModal(type, title, message, onConfirm = null) {
        const modal = document.getElementById('authModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');
        const modalIcon = document.getElementById('modalIcon');
        const modalButton = document.getElementById('modalButton');
        const modalClose = document.getElementById('modalClose');

        // Set modal content
        modalTitle.textContent = title;
        modalMessage.textContent = message;

        // Set icon based on type
        if (type === 'success') {
            modalIcon.innerHTML = '✅';
            modalIcon.className = 'modal-icon success';
            modalButton.textContent = 'Continue';
            modalButton.className = 'btn btn-success';
        } else if (type === 'error') {
            modalIcon.innerHTML = '❌';
            modalIcon.className = 'modal-icon error';
            modalButton.textContent = 'Try Again';
            modalButton.className = 'btn btn-danger';
        }

        // Show modal
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';

        // Handle modal close
        const closeModal = () => {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
            if (onConfirm) {
                onConfirm();
            }
        };

        // Event listeners
        modalButton.onclick = closeModal;
        modalClose.onclick = closeModal;

        // Close on outside click
        modal.onclick = (e) => {
            if (e.target === modal) {
                closeModal();
            }
        };

        // Close on escape key
        document.addEventListener('keydown', function escapeHandler(e) {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', escapeHandler);
            }
        });
    }
}
