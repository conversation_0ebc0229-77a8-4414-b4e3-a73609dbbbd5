/**
 * Admin Dashboard Manager
 * Handles user management, statistics, and admin functionality
 */

class AdminDashboard {
    constructor() {
        this.apiBaseUrl = '/api/admin';
        this.authToken = localStorage.getItem('authToken');
        this.currentPage = 1;
        this.usersPerPage = 10;
        this.totalUsers = 0;
        this.users = [];
        this.filteredUsers = [];
        
        this.initializeEventListeners();
        this.loadDashboardData();
    }

    // Initialize all event listeners
    initializeEventListeners() {
        // Logout button
        document.getElementById('adminLogout').addEventListener('click', () => this.logout());
        
        // User search
        document.getElementById('userSearch').addEventListener('input', (e) => this.searchUsers(e.target.value));
        
        // Refresh buttons
        document.getElementById('refreshUsers').addEventListener('click', () => this.loadUsers());
        document.getElementById('refreshActivity').addEventListener('click', () => this.loadActivity());
        
        // Export button
        document.getElementById('exportUsers').addEventListener('click', () => this.exportUsers());
        
        // Pagination
        document.getElementById('prevPage').addEventListener('click', () => this.changePage(-1));
        document.getElementById('nextPage').addEventListener('click', () => this.changePage(1));
        
        // Modal close buttons
        document.getElementById('userModalClose').addEventListener('click', () => this.closeModal('userModal'));
        document.getElementById('closeUserModal').addEventListener('click', () => this.closeModal('userModal'));
        document.getElementById('confirmModalClose').addEventListener('click', () => this.closeModal('confirmModal'));
        document.getElementById('cancelAction').addEventListener('click', () => this.closeModal('confirmModal'));
        
        // Delete user button
        document.getElementById('deleteUser').addEventListener('click', () => this.confirmDeleteUser());
        
        // Close modals on outside click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
    }

    // Load all dashboard data
    async loadDashboardData() {
        this.showLoading(true);
        
        try {
            await Promise.all([
                this.loadStats(),
                this.loadUsers(),
                this.loadActivity()
            ]);
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.showError('Failed to load dashboard data');
        } finally {
            this.showLoading(false);
        }
    }

    // Load dashboard statistics
    async loadStats() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/stats`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load stats');
            }

            const data = await response.json();
            
            if (data.success) {
                this.updateStats(data.data);
            }
        } catch (error) {
            console.error('Failed to load stats:', error);
            // Set default stats if API fails
            this.updateStats({
                totalUsers: this.users.length,
                newUsersToday: 0,
                totalBookings: 0,
                activeUsers: 0
            });
        }
    }

    // Update statistics display
    updateStats(stats) {
        document.getElementById('totalUsers').textContent = stats.totalUsers || 0;
        document.getElementById('newUsersToday').textContent = stats.newUsersToday || 0;
        document.getElementById('totalBookings').textContent = stats.totalBookings || 0;
        document.getElementById('activeUsers').textContent = stats.activeUsers || 0;
    }

    // Load users list
    async loadUsers() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/users`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load users');
            }

            const data = await response.json();
            
            if (data.success) {
                this.users = data.data.users || [];
                this.filteredUsers = [...this.users];
                this.totalUsers = this.users.length;
                this.renderUsers();
                this.updatePagination();
            }
        } catch (error) {
            console.error('Failed to load users:', error);
            this.showError('Failed to load users');
        }
    }

    // Search users
    searchUsers(query) {
        if (!query.trim()) {
            this.filteredUsers = [...this.users];
        } else {
            const searchTerm = query.toLowerCase();
            this.filteredUsers = this.users.filter(user => 
                user.first_name?.toLowerCase().includes(searchTerm) ||
                user.last_name?.toLowerCase().includes(searchTerm) ||
                user.email?.toLowerCase().includes(searchTerm) ||
                user.phone?.includes(searchTerm)
            );
        }
        
        this.currentPage = 1;
        this.renderUsers();
        this.updatePagination();
    }

    // Render users table
    renderUsers() {
        const tbody = document.getElementById('usersTableBody');
        const startIndex = (this.currentPage - 1) * this.usersPerPage;
        const endIndex = startIndex + this.usersPerPage;
        const pageUsers = this.filteredUsers.slice(startIndex, endIndex);

        tbody.innerHTML = pageUsers.map(user => `
            <tr>
                <td>${user.id}</td>
                <td>${user.first_name} ${user.last_name}</td>
                <td>${user.email}</td>
                <td>${user.phone || 'N/A'}</td>
                <td>${this.formatDate(user.created_at)}</td>
                <td>${user.last_login ? this.formatDate(user.last_login) : 'Never'}</td>
                <td>
                    <span class="status-badge ${user.is_active ? 'active' : 'inactive'}">
                        ${user.is_active ? 'Active' : 'Inactive'}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-small btn-primary" onclick="adminDashboard.viewUser(${user.id})">
                            View
                        </button>
                        <button class="btn btn-small btn-danger" onclick="adminDashboard.deleteUserPrompt(${user.id})">
                            Delete
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // Update pagination
    updatePagination() {
        const totalPages = Math.ceil(this.filteredUsers.length / this.usersPerPage);
        const startIndex = (this.currentPage - 1) * this.usersPerPage;
        const endIndex = Math.min(startIndex + this.usersPerPage, this.filteredUsers.length);

        // Update pagination info
        document.getElementById('paginationInfo').textContent = 
            `Showing ${startIndex + 1}-${endIndex} of ${this.filteredUsers.length} users`;

        // Update pagination buttons
        document.getElementById('prevPage').disabled = this.currentPage === 1;
        document.getElementById('nextPage').disabled = this.currentPage === totalPages || totalPages === 0;

        // Update page numbers
        const pageNumbers = document.getElementById('pageNumbers');
        pageNumbers.innerHTML = '';
        
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-number ${i === this.currentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => this.goToPage(i);
                pageNumbers.appendChild(pageBtn);
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                const dots = document.createElement('span');
                dots.textContent = '...';
                dots.className = 'page-dots';
                pageNumbers.appendChild(dots);
            }
        }
    }

    // Change page
    changePage(direction) {
        const totalPages = Math.ceil(this.filteredUsers.length / this.usersPerPage);
        const newPage = this.currentPage + direction;
        
        if (newPage >= 1 && newPage <= totalPages) {
            this.currentPage = newPage;
            this.renderUsers();
            this.updatePagination();
        }
    }

    // Go to specific page
    goToPage(page) {
        this.currentPage = page;
        this.renderUsers();
        this.updatePagination();
    }

    // View user details
    async viewUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        const userDetails = document.getElementById('userDetails');
        userDetails.innerHTML = `
            <div class="user-detail-grid">
                <div class="user-detail-item">
                    <label>ID:</label>
                    <span>${user.id}</span>
                </div>
                <div class="user-detail-item">
                    <label>Name:</label>
                    <span>${user.first_name} ${user.last_name}</span>
                </div>
                <div class="user-detail-item">
                    <label>Email:</label>
                    <span>${user.email}</span>
                </div>
                <div class="user-detail-item">
                    <label>Phone:</label>
                    <span>${user.phone || 'N/A'}</span>
                </div>
                <div class="user-detail-item">
                    <label>Joined:</label>
                    <span>${this.formatDate(user.created_at)}</span>
                </div>
                <div class="user-detail-item">
                    <label>Last Login:</label>
                    <span>${user.last_login ? this.formatDate(user.last_login) : 'Never'}</span>
                </div>
                <div class="user-detail-item">
                    <label>Status:</label>
                    <span class="status-badge ${user.is_active ? 'active' : 'inactive'}">
                        ${user.is_active ? 'Active' : 'Inactive'}
                    </span>
                </div>
            </div>
        `;

        this.currentUserId = userId;
        this.showModal('userModal');
    }

    // Load recent activity
    async loadActivity() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/activity`, {
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to load activity');
            }

            const data = await response.json();
            
            if (data.success) {
                this.renderActivity(data.data.activities || []);
            }
        } catch (error) {
            console.error('Failed to load activity:', error);
            // Show sample activity if API fails
            this.renderActivity([]);
        }
    }

    // Render activity list
    renderActivity(activities) {
        const activityList = document.getElementById('activityList');

        if (activities.length === 0) {
            activityList.innerHTML = `
                <div class="activity-item">
                    <div class="activity-icon login">👤</div>
                    <div class="activity-content">
                        <h4>No recent activity</h4>
                        <p>Activity will appear here as users interact with the system</p>
                    </div>
                    <div class="activity-time">Now</div>
                </div>
            `;
            return;
        }

        activityList.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon ${activity.type}">
                    ${this.getActivityIcon(activity.type)}
                </div>
                <div class="activity-content">
                    <h4>${activity.title}</h4>
                    <p>${activity.description}</p>
                </div>
                <div class="activity-time">${this.formatTimeAgo(activity.created_at)}</div>
            </div>
        `).join('');
    }

    // Get activity icon
    getActivityIcon(type) {
        const icons = {
            login: '🔑',
            signup: '👤',
            booking: '📅',
            payment: '💳',
            cancellation: '❌'
        };
        return icons[type] || '📝';
    }

    // Delete user prompt
    deleteUserPrompt(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        document.getElementById('confirmTitle').textContent = 'Delete User';
        document.getElementById('confirmMessage').textContent =
            `Are you sure you want to delete ${user.first_name} ${user.last_name}? This action cannot be undone.`;

        this.currentUserId = userId;
        this.showModal('confirmModal');

        // Set up confirm action
        document.getElementById('confirmAction').onclick = () => this.deleteUser(userId);
    }

    // Confirm delete user from user modal
    confirmDeleteUser() {
        if (!this.currentUserId) return;
        this.deleteUserPrompt(this.currentUserId);
        this.closeModal('userModal');
    }

    // Delete user
    async deleteUser(userId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/users/${userId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Failed to delete user');
            }

            const data = await response.json();

            if (data.success) {
                this.showSuccess('User deleted successfully');
                this.loadUsers(); // Reload users list
                this.loadStats(); // Update stats
            } else {
                throw new Error(data.error?.message || 'Failed to delete user');
            }
        } catch (error) {
            console.error('Failed to delete user:', error);
            this.showError('Failed to delete user: ' + error.message);
        } finally {
            this.closeModal('confirmModal');
        }
    }

    // Export users to CSV
    exportUsers() {
        const csvContent = this.generateCSV(this.filteredUsers);
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `users_export_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    // Generate CSV content
    generateCSV(users) {
        const headers = ['ID', 'First Name', 'Last Name', 'Email', 'Phone', 'Joined', 'Last Login', 'Status'];
        const csvRows = [headers.join(',')];

        users.forEach(user => {
            const row = [
                user.id,
                `"${user.first_name}"`,
                `"${user.last_name}"`,
                `"${user.email}"`,
                `"${user.phone || ''}"`,
                `"${this.formatDate(user.created_at)}"`,
                `"${user.last_login ? this.formatDate(user.last_login) : 'Never'}"`,
                `"${user.is_active ? 'Active' : 'Inactive'}"`
            ];
            csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
    }

    // Logout
    logout() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        window.location.href = '/login.html';
    }

    // Utility functions
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        overlay.style.display = show ? 'flex' : 'none';
    }

    showError(message) {
        alert('Error: ' + message); // You can replace this with a better notification system
    }

    showSuccess(message) {
        alert('Success: ' + message); // You can replace this with a better notification system
    }

    formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }

    formatTimeAgo(dateString) {
        if (!dateString) return 'Unknown';
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;
        return date.toLocaleDateString();
    }
}
