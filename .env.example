# ============================================================================
# AUTHENTICATION SYSTEM ENVIRONMENT CONFIGURATION
# Copy this file to .env and update the values for your environment
# ============================================================================

# Server Configuration
NODE_ENV=development
PORT=3001

# Database Configuration
DATABASE_PATH=./database/auth_system.db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-make-it-very-long-and-random
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=30d

# Security Configuration
BCRYPT_SALT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=15
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=10

# Email Configuration (for production)
EMAIL_SERVICE=gmail
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Frontend URLs (for CORS and redirects)
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5500

# Session Configuration
SESSION_CLEANUP_INTERVAL=3600000
TOKEN_CLEANUP_INTERVAL=********

# File Upload Configuration
MAX_FILE_SIZE=10mb
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/auth-system.log

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ENABLE_ADMIN_ROUTES=true

# Development Configuration
ENABLE_CORS=true
ENABLE_RATE_LIMITING=true
ENABLE_HELMET=true

# Database Backup Configuration
BACKUP_INTERVAL=********
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=./backups

# Notification Configuration
ENABLE_EMAIL_NOTIFICATIONS=false
ENABLE_SMS_NOTIFICATIONS=false

# Social Login Configuration (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Two-Factor Authentication (optional)
ENABLE_2FA=false
TOTP_SERVICE_NAME=YourApp
TOTP_ISSUER=YourCompany
