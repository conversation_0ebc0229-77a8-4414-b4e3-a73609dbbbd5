/* Sport Pages Specific Styles */

/* Sport Header */
.sport-header {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.football-header {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.tennis-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.padel-header {
    background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
}

.sport-header-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.sport-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.sport-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
    color: white;
}

.sport-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.sport-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.sport-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Pitch/Court Types */
.pitch-types,
.court-types {
    padding: 4rem 0;
    background: var(--background-light);
}

.pitch-types-grid,
.court-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.pitch-type-card,
.court-type-card {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.pitch-type-card:hover,
.court-type-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.pitch-type-image,
.court-type-image {
    height: 200px;
    overflow: hidden;
}

.pitch-type-image img,
.court-type-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.pitch-type-card:hover .pitch-type-image img,
.court-type-card:hover .court-type-image img {
    transform: scale(1.05);
}

.pitch-type-content,
.court-type-content {
    padding: 1.5rem;
}

.pitch-type-title,
.court-type-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.pitch-type-description,
.court-type-description {
    color: var(--text-light);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.pitch-features,
.court-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.pitch-features .feature,
.court-features .feature {
    background: var(--background-light);
    color: var(--text-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.pitch-pricing,
.court-pricing {
    margin-bottom: 1.5rem;
}

.price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
}

.price-note {
    font-size: 0.85rem;
    color: var(--text-light);
    margin-top: 0.25rem;
}

/* Venues Grid */
.football-venues,
.tennis-venues,
.padel-venues {
    padding: 4rem 0;
    background: var(--background-light);
}

.venues-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.venue-card {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.venue-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.venue-image {
    height: 200px;
    overflow: hidden;
}

.venue-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.venue-card:hover .venue-image img {
    transform: scale(1.05);
}

.venue-content {
    padding: 1.5rem;
}

.venue-name {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.venue-location {
    color: var(--text-light);
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.venue-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.stars {
    font-size: 0.9rem;
}

.rating-text {
    font-size: 0.85rem;
    color: var(--text-light);
}

.venue-pitches,
.venue-courts {
    margin-bottom: 1rem;
}

.pitch-info,
.court-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: var(--background-light);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.pitch-type,
.court-type {
    font-weight: 500;
}

.pitch-count,
.court-count {
    color: var(--text-light);
}

.pitch-price,
.court-price {
    font-weight: 600;
    color: var(--primary-color);
}

.venue-amenities {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.amenity {
    background: var(--background-light);
    color: var(--text-color);
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.btn-venue {
    background: var(--primary-color);
    color: white;
    width: 100%;
    text-align: center;
    text-decoration: none;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    display: block;
}

.btn-venue:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

/* Quick Book Section */
.quick-book-section {
    padding: 4rem 0;
    background: var(--secondary-color);
    color: white;
}

.quick-book-content {
    text-align: center;
    max-width: 1000px;
    margin: 0 auto;
}

.quick-book-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.quick-book-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.quick-book-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: white;
}

.form-group select,
.form-group input {
    padding: 0.75rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: white;
    color: var(--text-color);
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 220, 48, 0.3);
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Services Section */
.tennis-services {
    padding: 4rem 0;
    background: var(--background-color);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.service-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.service-description {
    color: var(--text-light);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.service-features .feature {
    background: var(--background-light);
    color: var(--text-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.service-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

/* About Padel Section */
.about-padel {
    padding: 4rem 0;
    background: var(--background-color);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-description {
    font-size: 1.1rem;
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 2rem;
}

.padel-features {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.feature-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.feature-content h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-color);
}

.feature-content p {
    color: var(--text-light);
    line-height: 1.5;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

/* Padel Packages */
.padel-packages {
    padding: 4rem 0;
    background: var(--background-light);
}

.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.package-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.package-card.featured {
    border: 2px solid var(--primary-color);
    transform: scale(1.05);
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.package-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.package-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.package-header {
    background: var(--background-light);
    padding: 2rem;
    text-align: center;
}

.package-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.package-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.package-duration {
    color: var(--text-light);
    font-size: 0.9rem;
}

.package-content {
    padding: 2rem;
}

.package-description {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.package-features {
    list-style: none;
    margin-bottom: 2rem;
}

.package-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

.package-features li:last-child {
    border-bottom: none;
}

.btn-package {
    background: var(--primary-color);
    color: white;
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-package:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.btn-featured {
    background: var(--secondary-color);
}

.btn-featured:hover {
    background: #333;
}

/* Rules Section */
.padel-rules {
    padding: 4rem 0;
    background: var(--background-color);
}

.rules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.rule-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.rule-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.rule-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1rem;
}

.rule-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.rule-description {
    color: var(--text-light);
    line-height: 1.6;
}

/* Tips Section */
.football-tips,
.tennis-tips,
.padel-tips {
    padding: 4rem 0;
    background: var(--background-light);
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.tip-card {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.tip-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.tip-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.tip-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.tip-list {
    list-style: none;
}

.tip-list li {
    padding: 0.5rem 0;
    color: var(--text-light);
    position: relative;
    padding-left: 1.5rem;
}

.tip-list li::before {
    content: '•';
    color: var(--primary-color);
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sport-header-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .sport-title {
        font-size: 2.5rem;
    }
    
    .sport-stats {
        justify-content: center;
        gap: 1rem;
    }
    
    .pitch-types-grid,
    .court-types-grid {
        grid-template-columns: 1fr;
    }
    
    .venues-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .packages-grid {
        grid-template-columns: 1fr;
    }
    
    .package-card.featured {
        transform: none;
    }
    
    .package-card.featured:hover {
        transform: translateY(-5px);
    }
    
    .rules-grid {
        grid-template-columns: 1fr;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .sport-header {
        padding: 2rem 0;
    }
    
    .sport-title {
        font-size: 2rem;
    }
    
    .sport-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .quick-book-title {
        font-size: 2rem;
    }
    
    .quick-book-form {
        padding: 1.5rem;
    }
}