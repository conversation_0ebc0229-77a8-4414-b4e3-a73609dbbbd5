/* Contact Page Specific Styles */

.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Contact Methods */
.contact-methods {
    padding: 4rem 0;
    background: var(--background-color);
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.contact-card {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.contact-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.contact-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.contact-description {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.contact-details {
    margin-bottom: 1.5rem;
}

.contact-info {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.contact-hours {
    font-size: 0.9rem;
    color: var(--text-light);
}

.btn-contact {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-block;
}

.btn-contact:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

/* Contact Form */
.contact-form-section {
    padding: 4rem 0;
    background: var(--background-light);
}

.contact-form-container {
    max-width: 800px;
    margin: 0 auto;
    background: var(--background-color);
    border: 1px solid var(--border-color);
    padding: 3rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.form-subtitle {
    color: var(--text-light);
    line-height: 1.6;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 220, 48, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* FAQ Section */
.faq-section {
    padding: 4rem 0;
    background: var(--background-color);
}

.faq-container {
    max-width: 800px;
    margin: 2rem auto 0;
}

.faq-item {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1rem;
    overflow: hidden;
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.faq-question:hover {
    background: var(--background-light);
}

.faq-question h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    transition: var(--transition);
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
    max-height: 200px;
}

.faq-answer p {
    padding: 0 1.5rem 1.5rem;
    color: var(--text-light);
    line-height: 1.6;
    margin: 0;
}

/* Social Section */
.social-section {
    padding: 4rem 0;
    background: var(--background-light);
}

.social-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.social-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.social-subtitle {
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.social-links-large {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.social-link-large {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-color);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
    min-width: 200px;
}

.social-link-large:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.social-link-large.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
    color: white;
}

.social-link-large.facebook:hover {
    background: #1877f2;
    color: white;
}

.social-link-large.twitter:hover {
    background: #1da1f2;
    color: white;
}

.social-icon {
    font-size: 2rem;
}

.social-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.social-info p {
    font-size: 0.9rem;
    opacity: 0.8;
    margin: 0;
}

/* Live Chat Modal */
.chat-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.chat-modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    position: relative;
}

.chat-close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
}

.chat-close:hover {
    color: var(--text-color);
}

.chat-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.chat-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.chat-subtitle {
    color: var(--text-light);
}

.chat-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chat-form input,
.chat-form textarea {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
}

.chat-form input:focus,
.chat-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Map Modal */
.map-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.map-modal-content {
    background-color: white;
    margin: 2% auto;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 800px;
    height: 80%;
    position: relative;
    overflow: hidden;
}

.map-close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    z-index: 1;
    background: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow);
}

.map-close:hover {
    color: var(--text-color);
}

.map-placeholder {
    width: 100%;
    height: 100%;
    background: var(--background-light);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: var(--text-light);
}

.map-placeholder h3 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-form-container {
        padding: 2rem;
        margin: 0 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .social-links-large {
        flex-direction: column;
        align-items: center;
    }
    
    .social-link-large {
        width: 100%;
        max-width: 300px;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .form-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 2rem 0;
    }
    
    .contact-methods {
        padding: 3rem 0;
    }
    
    .contact-form-section {
        padding: 3rem 0;
    }
    
    .faq-section {
        padding: 3rem 0;
    }
    
    .social-section {
        padding: 3rem 0;
    }
    
    .contact-form-container {
        padding: 1.5rem;
    }
    
    .chat-modal-content,
    .map-modal-content {
        margin: 10% auto;
        width: 95%;
    }
}