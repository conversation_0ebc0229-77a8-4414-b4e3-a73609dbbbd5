<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sirens FC - Book a Pitch</title>
    <meta name="description" content="Book your football pitch at Sirens FC. Choose your preferred date, time, and pitch section.">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/sirens-booking.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="nav-container">
                <div class="nav-logo">
                    <a href="index.html">
                        <span class="logo-text">SM</span>
                        <span class="logo-subtitle">Sports Malta</span>
                    </a>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="index.html" class="nav-link">Home</a>
                        </li>
                        <li class="nav-item">
                            <a href="booking.html" class="nav-link">Book Now</a>
                        </li>
                        <li class="nav-item">
                            <a href="contact.html" class="nav-link">Contact</a>
                        </li>
                        <li class="nav-item">
                            <a href="account.html" class="nav-link">Account</a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="sirens-booking">
        <div class="container">
            <h1 class="page-title">Sirens FC - Pitch Booking</h1>
            
            <div class="booking-container">
                <!-- Left Side: Calendar and Form -->
                <div class="left-section">
                    <div class="calendar-section">
                        <div class="date-picker-container">
                            <h3 class="section-title">Select Date</h3>
                            <div class="calendar-container">
                                <div class="calendar-header">
                                    <button type="button" id="prevMonth">❮</button>
                                    <h4 id="currentMonth">July 2025</h4>
                                    <button type="button" id="nextMonth">❯</button>
                                </div>
                                <div class="weekdays">
                                    <div>Sun</div>
                                    <div>Mon</div>
                                    <div>Tue</div>
                                    <div>Wed</div>
                                    <div>Thu</div>
                                    <div>Fri</div>
                                    <div>Sat</div>
                                </div>
                                <div class="days" id="calendarDays">
                                    <!-- Calendar days will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Booking Summary -->
                    <div class="booking-form" id="bookingForm">
                        <h3>Booking Summary</h3>
                        
                        <div class="booking-details">
                            <div class="detail-row">
                                <span class="label">Name:</span>
                                <span class="value" id="displayName">-</span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Email:</span>
                                <span class="value" id="displayEmail">-</span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Date:</span>
                                <span class="value" id="displayDate">-</span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Time:</span>
                                <span class="value" id="displayTime">-</span>
                            </div>
                            <div class="detail-row">
                                <span class="label">Pitch:</span>
                                <span class="value" id="displayPitch">-</span>
                            </div>
                            <div class="detail-row total-row">
                                <span class="label">Total Price:</span>
                                <span class="value" id="displayPrice">€0</span>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="notes">Additional Notes (Optional)</label>
                            <textarea id="notes" name="notes" rows="3" placeholder="Any special requests or requirements..."></textarea>
                        </div>
                        
                        <button type="button" class="btn btn-book" id="bookNowBtn" disabled>Book Now</button>
                    </div>
                </div>
                
                <!-- Right Side: Time Slots and Pitch -->
                <div class="right-section">
                    <div class="time-slots">
                        <h3>Available Times</h3>
                        <div class="time-grid" id="timeSlots">
                            <!-- Time slots will be populated by JavaScript -->
                        </div>
                    </div>
                    
                    <div class="pitch-container">
                        <div class="pitch-options">
                            <button type="button" class="btn-pitch-option active" data-option="full">Full Pitch</button>
                        </div>
                        <div class="football-pitch">
                            <div class="pitch">
                                <div class="half left-half">
                                    <button type="button" class="btn-pitch-half left" data-side="left">Left Side</button>
                                </div>
                                <div class="half right-half">
                                    <button type="button" class="btn-pitch-half right" data-side="right">Right Side</button>
                                </div>
                                <div class="center-circle"></div>
                                <div class="center-line"></div>
                            </div>
                        </div>
                        <div class="pitch-legend">
                            <div class="legend-item">
                                <span class="legend-color full"></span>
                                <span>Available</span>
                            </div>
                            <div class="legend-item">
                                <span class="legend-color booked"></span>
                                <span>Booked</span>
                            </div>
                            <div class="legend-item">
                                <span class="legend-color selected"></span>
                                <span>Selected</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Booking Confirmation Modal -->
    <div id="bookingConfirmationModal" class="modal" style="display:none;">
        <div class="modal-content">
            <span class="close" id="closeConfirmationModal">&times;</span>
            <h2>Booking Confirmed!</h2>
            <div class="confirmation-details">
                <p><strong>Reference:</strong> <span id="confirmBookingRef">-</span></p>
                <p><strong>Date:</strong> <span id="confirmBookingDate">-</span></p>
                <p><strong>Time:</strong> <span id="confirmBookingTime">-</span></p>
                <p><strong>Pitch:</strong> <span id="confirmBookingPitch">-</span></p>
                <p><strong>Price:</strong> <span id="confirmBookingPrice">-</span></p>
                <p><strong>Email:</strong> <span id="confirmBookingEmail">-</span></p>
            </div>
            <button id="goToAccountBtn" class="btn btn-primary">Go to My Bookings</button>
        </div>
    </div>
</main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Sports Malta</h3>
                    <p>Your premier destination for sports facilities in Malta.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="booking.html">Book Now</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +356 1234 5678</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Sports Malta. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Authentication Check Script -->
    <script>
        // Check authentication before loading the page
        document.addEventListener('DOMContentLoaded', async function() {
            // Wait for auth handler to be loaded
            if (!window.authHandler) {
                // If auth handler not loaded, redirect to login
                window.location.href = '/login.html?returnUrl=' + encodeURIComponent(window.location.pathname);
                return;
            }
            
            const authStatus = await window.authHandler.checkAuthStatus();
            
            if (!authStatus.success) {
                // User is not authenticated, redirect to login
                window.authHandler.redirectToLogin('/sirens-booking.html');
                return;
            }
            
            // User is authenticated, continue loading the page
            console.log('User authenticated for booking:', authStatus.user);
        });
    </script>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/sirens-booking.js"></script>
</body>
</html>
