{"name": "modern-auth-system", "version": "1.0.0", "description": "Complete authentication and user management system built from scratch", "main": "auth-server.js", "scripts": {"start": "node auth-server.js", "dev": "nodemon auth-server.js", "test": "jest", "test:watch": "jest --watch", "db:backup": "node scripts/backup-database.js", "db:restore": "node scripts/restore-database.js", "db:migrate": "node scripts/migrate-database.js", "db:seed": "node scripts/seed-database.js", "cleanup": "node scripts/cleanup.js"}, "keywords": ["authentication", "user-management", "jwt", "sqlite", "express", "security", "nodejs"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "validator": "^13.11.0", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/modern-auth-system.git"}, "bugs": {"url": "https://github.com/yourusername/modern-auth-system/issues"}, "homepage": "https://github.com/yourusername/modern-auth-system#readme"}