/* Account Page Styles */

.account-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
}

.account-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.user-details h1 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
}

.user-details p {
    margin: 0 0 1rem 0;
    opacity: 0.9;
}

.user-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.account-nav {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.account-tabs {
    display: flex;
    gap: 0;
    overflow-x: auto;
}

.tab-btn {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: #718096;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn:hover {
    color: #667eea;
    background: #f7fafc;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: #f7fafc;
}

.tab-content {
    display: none;
    padding: 2rem 0;
}

.tab-content.active {
    display: block;
}

.dashboard-section {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #2d3748;
}

.bookings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.booking-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.booking-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.booking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.booking-sport {
    font-weight: 600;
    color: #2d3748;
}

.booking-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.booking-status.confirmed {
    background: #c6f6d5;
    color: #38a169;
}

.booking-status.pending {
    background: #fef5e7;
    color: #d69e2e;
}

.booking-status.completed {
    background: #e6fffa;
    color: #319795;
}

.booking-status.cancelled {
    background: #fed7d7;
    color: #e53e3e;
}

.booking-venue {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: #2d3748;
}

.booking-info {
    margin: 0 0 0.5rem 0;
    color: #718096;
}

.booking-date {
    font-weight: 500;
    margin-right: 1rem;
}

.booking-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2rem;
}

.stat-content .stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.stat-content .stat-label {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.stat-change {
    color: #38a169;
    font-size: 0.75rem;
}

.venues-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.venue-card-small {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.venue-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    background: #f7fafc;
    overflow: hidden;
}

.venue-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.venue-info {
    flex: 1;
}

.venue-name {
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: #2d3748;
}

.venue-location {
    color: #718096;
    font-size: 0.875rem;
    margin: 0 0 0.5rem 0;
}

.venue-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
}

.booking-count {
    color: #718096;
}

.venue-rating {
    color: #d69e2e;
}

.bookings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.bookings-filters {
    display: flex;
    gap: 1rem;
}

.bookings-filters select {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #2d3748;
}

.bookings-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.booking-item {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.booking-date-badge {
    text-align: center;
    min-width: 60px;
}

.date-day {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2d3748;
}

.date-month {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
}

.booking-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.booking-title {
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: #2d3748;
}

.booking-details-text {
    color: #718096;
    margin: 0 0 0.5rem 0;
}

.booking-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
}

.booking-ref {
    color: #718096;
}

.booking-price {
    font-weight: 600;
    color: #2d3748;
}

.booking-status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.booking-actions-list {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.profile-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.profile-form {
    max-width: 600px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2d3748;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.password-form {
    max-width: 400px;
}

.preferences-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.preference-group {
    margin-bottom: 2rem;
}

.preference-group h3 {
    margin-bottom: 1rem;
    color: #2d3748;
}

.preference-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.preference-info h4 {
    margin: 0 0 0.25rem 0;
    color: #2d3748;
}

.preference-info p {
    margin: 0;
    color: #718096;
    font-size: 0.875rem;
}

.sports-preferences {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.sport-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: #f7fafc;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.sport-checkbox:hover {
    background: #edf2f7;
}

.sport-checkbox input:checked + .sport-icon + .sport-name {
    color: #667eea;
    font-weight: 600;
}

.sport-icon {
    font-size: 1.25rem;
}

.payment-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.payment-methods {
    max-width: 600px;
}

.payment-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.card-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-icon {
    font-size: 1.5rem;
}

.card-details h3 {
    margin: 0 0 0.25rem 0;
    color: #2d3748;
}

.card-details p {
    margin: 0;
    color: #718096;
    font-size: 0.875rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.add-payment {
    text-align: center;
    padding: 2rem;
    border: 2px dashed #e2e8f0;
    border-radius: 8px;
}

.billing-history {
    max-width: 800px;
}

.billing-item {
    display: grid;
    grid-template-columns: 120px 1fr 100px 80px 100px;
    gap: 1rem;
    align-items: center;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
    margin-bottom: 0.5rem;
}

.billing-date {
    font-weight: 500;
    color: #2d3748;
}

.billing-description {
    color: #718096;
}

.billing-amount {
    font-weight: 600;
    color: #2d3748;
}

.billing-status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
}

.billing-status.paid {
    background: #c6f6d5;
    color: #38a169;
}

.no-bookings {
    text-align: center;
    padding: 3rem;
    color: #718096;
}

.no-bookings p {
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.message {
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .account-header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .user-stats {
        justify-content: center;
    }
    
    .quick-actions {
        justify-content: center;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .booking-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .booking-content {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .booking-actions-list {
        width: 100%;
        justify-content: flex-start;
    }
    
    .billing-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
        text-align: left;
    }
    
    .bookings-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .bookings-filters {
        width: 100%;
        justify-content: flex-start;
    }
    
    .bookings-filters select {
        flex: 1;
    }
}

@media (max-width: 480px) {
    .account-header {
        padding: 2rem 0;
    }
    
    .user-avatar {
        width: 60px;
        height: 60px;
        font-size: 1.25rem;
    }
    
    .user-details h1 {
        font-size: 1.5rem;
    }
    
    .user-stats {
        gap: 1rem;
    }
    
    .quick-actions {
        width: 100%;
    }
    
    .quick-actions .btn {
        flex: 1;
        text-align: center;
    }
    
    .bookings-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .venues-grid {
        grid-template-columns: 1fr;
    }
}