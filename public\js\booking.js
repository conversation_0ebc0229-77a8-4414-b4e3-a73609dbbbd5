/**
 * Booking System JavaScript
 * Handles facility booking functionality with authentication
 */
class BookingManager {
    constructor() {
        this.apiBaseUrl = '/api/bookings';
        this.facilities = [];
        this.selectedFacility = null;
        this.selectedDate = null;
        this.selectedTime = null;
        this.availableSlots = [];
        this.init();
    }

    async init() {
        // Check authentication
        if (!window.authHandler || !window.authHandler.isAuthenticated()) {
            this.showAuthRequired();
            return;
        }

        // Load facilities
        await this.loadFacilities();
        
        // Initialize event listeners
        this.initEventListeners();
        
        // Initialize forms
        this.initBookingForms();
    }

    showAuthRequired() {
        const bookingContainer = document.querySelector('.simple-booking-form, .quick-booking-form');
        if (bookingContainer) {
            bookingContainer.innerHTML = `
                <div class="auth-required">
                    <h3>Authentication Required</h3>
                    <p>Please log in to make a booking.</p>
                    <div class="auth-buttons">
                        <a href="/login.html?returnUrl=${encodeURIComponent(window.location.pathname)}" class="btn btn-primary">Login</a>
                        <a href="/signup.html" class="btn btn-secondary">Sign Up</a>
                    </div>
                </div>
            `;
        }
    }

    async loadFacilities() {
        try {
            const response = await window.authHandler.makeAuthenticatedRequest(`${this.apiBaseUrl}/facilities`);
            if (!response) return;

            const data = await response.json();
            if (data.success) {
                this.facilities = data.data.facilities;
                this.populateFacilitySelects();
            } else {
                console.error('Failed to load facilities:', data.error);
                this.showError('Failed to load facilities');
            }
        } catch (error) {
            console.error('Error loading facilities:', error);
            this.showError('Error loading facilities');
        }
    }

    populateFacilitySelects() {
        const facilitySelects = document.querySelectorAll('select[name="facility"], select[name="sport"], #bookingFacility');
        
        facilitySelects.forEach(select => {
            // Clear existing options except the first one
            const firstOption = select.querySelector('option[value=""]');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            // Add facility options
            this.facilities.forEach(facility => {
                const option = document.createElement('option');
                option.value = facility.id;
                option.textContent = `${facility.name} - ${facility.type} (${facility.location})`;
                option.dataset.type = facility.type;
                option.dataset.pricing = JSON.stringify({
                    full: facility.pricing_full,
                    half: facility.pricing_half
                });
                select.appendChild(option);
            });
        });
    }

    initEventListeners() {
        // Facility selection change
        document.addEventListener('change', (e) => {
            if (e.target.matches('select[name="facility"], select[name="sport"], #bookingFacility')) {
                this.onFacilityChange(e.target);
            }
        });

        // Date selection change
        document.addEventListener('change', (e) => {
            if (e.target.matches('input[type="date"], #bookingDate')) {
                this.onDateChange(e.target);
            }
        });

        // Time selection change
        document.addEventListener('change', (e) => {
            if (e.target.matches('select[name="time"], #bookingTime')) {
                this.onTimeChange(e.target);
            }
        });
    }

    initBookingForms() {
        // Simple booking form
        const simpleForm = document.getElementById('bookingForm');
        if (simpleForm) {
            simpleForm.addEventListener('submit', (e) => this.handleSimpleBooking(e));
        }

        // Quick booking form
        const quickForm = document.getElementById('quick-booking-form');
        if (quickForm) {
            quickForm.addEventListener('submit', (e) => this.handleQuickBooking(e));
        }

        // Set minimum date to today
        const dateInputs = document.querySelectorAll('input[type="date"]');
        const today = new Date().toISOString().split('T')[0];
        dateInputs.forEach(input => {
            input.min = today;
            
            // Set max date to 3 months from now
            const maxDate = new Date();
            maxDate.setMonth(maxDate.getMonth() + 3);
            input.max = maxDate.toISOString().split('T')[0];
        });
    }

    async onFacilityChange(select) {
        const facilityId = select.value;
        if (!facilityId) {
            this.selectedFacility = null;
            return;
        }

        this.selectedFacility = this.facilities.find(f => f.id == facilityId);
        
        // Update pricing display if exists
        this.updatePricingDisplay();
        
        // Reload availability if date is selected
        if (this.selectedDate) {
            await this.loadAvailability();
        }
    }

    async onDateChange(input) {
        this.selectedDate = input.value;
        
        if (this.selectedFacility && this.selectedDate) {
            await this.loadAvailability();
        }
    }

    onTimeChange(select) {
        this.selectedTime = select.value;
        this.updateBookingTotal();
    }

    async loadAvailability() {
        if (!this.selectedFacility || !this.selectedDate) return;

        try {
            const response = await window.authHandler.makeAuthenticatedRequest(
                `${this.apiBaseUrl}/availability/${this.selectedFacility.id}?date=${this.selectedDate}`
            );
            if (!response) return;

            const data = await response.json();
            if (data.success) {
                this.availableSlots = data.data.availableSlots;
                this.populateTimeSlots();
            } else {
                console.error('Failed to load availability:', data.error);
                this.showError('Failed to load availability');
            }
        } catch (error) {
            console.error('Error loading availability:', error);
            this.showError('Error loading availability');
        }
    }

    populateTimeSlots() {
        const timeSelects = document.querySelectorAll('select[name="time"], #bookingTime');
        
        timeSelects.forEach(select => {
            // Clear existing options except the first one
            const firstOption = select.querySelector('option[value=""]');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            // Add available time slots
            this.availableSlots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot.time;
                option.textContent = `${slot.time} - ${slot.duration} (€${slot.price})`;
                option.dataset.duration = slot.duration;
                option.dataset.price = slot.price;
                select.appendChild(option);
            });
        });
    }

    updatePricingDisplay() {
        const pricingElements = document.querySelectorAll('.pricing-display, .facility-pricing');
        
        if (this.selectedFacility) {
            pricingElements.forEach(element => {
                element.innerHTML = `
                    <div class="pricing-info">
                        <h4>${this.selectedFacility.name}</h4>
                        <p class="pricing-details">
                            Full Court: €${this.selectedFacility.pricing_full}/hour<br>
                            Half Court: €${this.selectedFacility.pricing_half}/hour
                        </p>
                    </div>
                `;
            });
        }
    }

    updateBookingTotal() {
        const totalElements = document.querySelectorAll('.booking-total, .total-price');
        
        if (this.selectedTime) {
            const selectedSlot = this.availableSlots.find(slot => slot.time === this.selectedTime);
            if (selectedSlot) {
                totalElements.forEach(element => {
                    element.innerHTML = `
                        <div class="total-breakdown">
                            <p>Duration: ${selectedSlot.duration}</p>
                            <p class="total-amount">Total: €${selectedSlot.price}</p>
                        </div>
                    `;
                });
            }
        }
    }

    async handleSimpleBooking(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        // Convert simple form data to booking format
        const bookingData = {
            facilityId: parseInt(formData.get('facility') || this.selectedFacility?.id),
            date: formData.get('date') || this.selectedDate,
            time: formData.get('time') || this.selectedTime,
            duration: 'full', // Default for simple form
            notes: formData.get('notes') || ''
        };

        await this.submitBooking(bookingData, form);
    }

    async handleQuickBooking(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        const bookingData = {
            facilityId: parseInt(formData.get('sport')), // Using sport select for facility
            date: formData.get('date'),
            time: formData.get('time'),
            duration: formData.get('duration') || 'full',
            notes: formData.get('notes') || ''
        };

        await this.submitBooking(bookingData, form);
    }

    async submitBooking(bookingData, form) {
        // Validate required fields
        if (!bookingData.facilityId || !bookingData.date || !bookingData.time) {
            this.showError('Please fill in all required fields');
            return;
        }

        // Show loading state
        const submitButton = form.querySelector('button[type="submit"]');
        this.setButtonLoading(submitButton, true);

        try {
            const response = await window.authHandler.makeAuthenticatedRequest(`${this.apiBaseUrl}`, {
                method: 'POST',
                body: JSON.stringify(bookingData)
            });

            if (!response) return;

            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Booking confirmed! You will receive a confirmation email shortly.');
                form.reset();
                this.selectedFacility = null;
                this.selectedDate = null;
                this.selectedTime = null;
                
                // Redirect to account page after a delay
                setTimeout(() => {
                    window.location.href = '/account.html';
                }, 2000);
            } else {
                this.showError(data.error.message || 'Booking failed');
            }
        } catch (error) {
            console.error('Booking submission error:', error);
            this.showError('An error occurred while processing your booking');
        } finally {
            this.setButtonLoading(submitButton, false);
        }
    }

    setButtonLoading(button, isLoading) {
        if (!button) return;

        if (isLoading) {
            button.disabled = true;
            button.dataset.originalText = button.textContent;
            button.innerHTML = '<span class="loading-spinner"></span> Processing...';
        } else {
            button.disabled = false;
            button.textContent = button.dataset.originalText || 'Book Now';
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existing = document.querySelectorAll('.booking-notification');
        existing.forEach(el => el.remove());

        // Create notification
        const notification = document.createElement('div');
        notification.className = `booking-notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Add close functionality
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Initialize booking manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait for auth handler to be available
    const initBooking = () => {
        if (window.authHandler) {
            window.bookingManager = new BookingManager();
        } else {
            setTimeout(initBooking, 100);
        }
    };
    
    initBooking();
});
