const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const validator = require('validator');
const path = require('path');
const multer = require('multer');
require('dotenv').config();

// Mock email service to prevent crashes
const emailService = {
    testEmailConfiguration: async () => {
        console.log('📧 Email service disabled for development');
        return false;
    },
    sendWelcomeEmail: async (email, name) => {
        console.log(`📧 Would send welcome email to ${email} (${name})`);
        return { success: true };
    },
    sendPasswordResetEmail: async (email, name, resetUrl) => {
        console.log(`📧 Would send password reset email to ${email}`);
        return { success: true };
    },
    sendCheckoutNotification: async (orderNumber, orderData, total, userInfo) => {
        console.log(`📧 Would send checkout notification for order ${orderNumber}`);
        return { success: true };
    },
    sendOrderConfirmation: async (email, firstName, orderNumber, orderData, userInfo) => {
        console.log(`📧 Would send order confirmation to ${email} for order ${orderNumber}`);
        return { success: true };
    },
    sendOwnerNotification: async (orderNumber, orderData, total, userInfo) => {
        console.log(`📧 Would send owner notification for order ${orderNumber}`);
        return { success: true };
    },
    sendMassEmail: async (recipients, subject, message) => {
        console.log(`📧 Would send mass email to ${recipients.length} recipients: ${subject}`);
        return { success: true, totalSent: recipients.length, details: [] };
    },
    sendNewsletterWelcomeEmail: async (email) => {
        console.log(`📧 Would send newsletter welcome email to ${email}`);
        return { success: true };
    },
    sendNewsletterNotificationEmail: async (email) => {
        console.log(`📧 Would send newsletter notification email for ${email}`);
        return { success: true };
    },
    sendTestImageEmail: async (imageBuffer, fileName) => {
        console.log(`📧 Would send test image email with ${fileName}`);
        return { success: true };
    }
};

// Initialize Stripe
const stripeSecretKey = process.env.STRIPE_SECRET_KEY || 'sk_test_your_stripe_secret_key_here';
const stripe = require('stripe')(stripeSecretKey);

// Check if we're using real Stripe keys
const isLiveMode = stripeSecretKey.startsWith('sk_live_');
const isTestMode = stripeSecretKey.startsWith('sk_test_');
const isConfigured = isLiveMode || isTestMode;

const app = express();
const PORT = process.env.PORT || 3001;
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// Security middleware
app.use(helmet({
    contentSecurityPolicy: false, // Disable for development
    crossOriginEmbedderPolicy: false
}));

app.use(cors({
    origin: ['http://localhost:3000', 'http://127.0.0.1:3000', 'file://'],
    credentials: true
}));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// Stricter rate limiting for auth endpoints
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 50, // limit each IP to 50 requests per windowMs (increased for testing)
    message: { error: 'Too many authentication attempts, please try again later.' } // Return JSON format
});

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Configure multer for file uploads
const storage = multer.memoryStorage(); // Store files in memory for email attachment
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        // Only allow image files
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('Only image files are allowed!'), false);
        }
    }
});

// Serve static files
app.use(express.static('.'));

// Database setup
const db = new sqlite3.Database('./database/users.db', (err) => {
    if (err) {
        console.error('Error opening database:', err.message);
    } else {
        console.log('Connected to SQLite database.');
        initializeDatabase();
    }
});

// Initialize database tables
function initializeDatabase() {
    // Drop and recreate users table to ensure correct schema
    const dropUsersTable = `DROP TABLE IF EXISTS users`;

    const createUsersTable = `
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            registration_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME,
            is_active BOOLEAN DEFAULT 1,
            email_verified BOOLEAN DEFAULT 0,
            email_preferences TEXT DEFAULT '{"newsletter":true,"orders":true,"promotions":true}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `;

    // Newsletter subscribers table
    const createNewsletterTable = `
        CREATE TABLE IF NOT EXISTS newsletter_subscribers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            subscribed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            source TEXT DEFAULT 'website',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `;

    const createSessionsTable = `
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            token TEXT NOT NULL,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `;

    // Wishlist table
    const createWishlistTable = `
        CREATE TABLE IF NOT EXISTS user_wishlist (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            UNIQUE(user_id, product_id)
        )
    `;

    const createVerificationTokensTable = `
        CREATE TABLE IF NOT EXISTS verification_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            token TEXT NOT NULL UNIQUE,
            expires_at DATETIME NOT NULL,
            used BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `;

    // Orders table - stores order information (NO CARD DETAILS)
    const createOrdersTable = `
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            order_number TEXT UNIQUE NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            currency TEXT DEFAULT 'USD',
            status TEXT DEFAULT 'pending',
            payment_status TEXT DEFAULT 'pending',
            payment_method TEXT,
            payment_processor_id TEXT,
            shipping_address TEXT,
            billing_address TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `;

    // Order items table
    const createOrderItemsTable = `
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            product_name TEXT NOT NULL,
            product_type TEXT NOT NULL,
            quantity INTEGER NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            total_price DECIMAL(10,2) NOT NULL,
            customization_data TEXT,
            image_data TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders (id)
        )
    `;

    // Password reset tokens table
    const createPasswordResetTokensTable = `
        CREATE TABLE IF NOT EXISTS password_reset_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            token TEXT NOT NULL UNIQUE,
            expires_at DATETIME NOT NULL,
            used BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `;

    // Payment transactions table (NO CARD DETAILS)
    const createTransactionsTable = `
        CREATE TABLE IF NOT EXISTS payment_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            transaction_id TEXT UNIQUE NOT NULL,
            payment_processor TEXT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            currency TEXT DEFAULT 'USD',
            status TEXT NOT NULL,
            processor_response TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders (id)
        )
    `;

    // Login attempts table - tracks all login attempts
    const createLoginAttemptsTable = `
        CREATE TABLE IF NOT EXISTS login_attempts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT NOT NULL,
            success BOOLEAN NOT NULL,
            ip_address TEXT,
            user_agent TEXT,
            error_message TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `;

    // First drop the users table if it exists
    db.run(dropUsersTable, (err) => {
        if (err) {
            console.error('Error dropping users table:', err.message);
        } else {
            console.log('Users table dropped (if existed).');
        }

        // Then create the users table with correct schema
        db.run(createUsersTable, (err) => {
            if (err) {
                console.error('Error creating users table:', err.message);
            } else {
                console.log('Users table ready.');
            }
        });
    });

    db.run(createSessionsTable, (err) => {
        if (err) {
            console.error('Error creating sessions table:', err.message);
        } else {
            console.log('Sessions table ready.');
        }
    });

    db.run(createNewsletterTable, (err) => {
        if (err) {
            console.error('Error creating newsletter_subscribers table:', err.message);
        } else {
            console.log('Newsletter subscribers table ready.');
        }
    });

    db.run(createOrdersTable, (err) => {
        if (err) {
            console.error('Error creating orders table:', err.message);
        } else {
            console.log('Orders table ready.');
        }
    });

    db.run(createWishlistTable, (err) => {
        if (err) {
            console.error('Error creating wishlist table:', err.message);
        } else {
            console.log('Wishlist table ready.');
        }
    });

    db.run(createOrdersTable, (err) => {
        if (err) {
            console.error('Error creating orders table:', err.message);
        } else {
            console.log('Orders table ready.');
        }
    });

    db.run(createOrderItemsTable, (err) => {
        if (err) {
            console.error('Error creating order_items table:', err.message);
        } else {
            console.log('Order items table ready.');
        }
    });

    db.run(createTransactionsTable, (err) => {
        if (err) {
            console.error('Error creating payment_transactions table:', err.message);
        } else {
            console.log('Payment transactions table ready.');
        }
    });

    db.run(createPasswordResetTokensTable, (err) => {
        if (err) {
            console.error('Error creating password_reset_tokens table:', err.message);
        } else {
            console.log('Password reset tokens table ready.');
        }
    });

    db.run(createVerificationTokensTable, (err) => {
        if (err) {
            console.error('Error creating verification_tokens table:', err.message);
        } else {
            console.log('Verification tokens table ready.');
        }
    });

    db.run(createLoginAttemptsTable, (err) => {
        if (err) {
            console.error('Error creating login_attempts table:', err.message);
        } else {
            console.log('Login attempts table ready.');
        }
    });

    // Add email_preferences column if it doesn't exist (migration)
    db.run(`ALTER TABLE users ADD COLUMN email_preferences TEXT DEFAULT '{"newsletter":true,"orders":true,"promotions":true}'`, (err) => {
        if (err && !err.message.includes('duplicate column name')) {
            console.error('Error adding email_preferences column:', err.message);
        } else if (!err) {
            console.log('Email preferences column added.');
        }
    });
}

// Utility functions
function validateEmail(email) {
    return validator.isEmail(email) && email.length <= 255;
}

function validatePassword(password) {
    return password && password.length >= 6 && password.length <= 128;
}

function validateName(name) {
    return name && name.trim().length >= 2 && name.trim().length <= 100;
}

// Middleware to verify JWT token
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
}

// Optional middleware to extract user info if token is present (doesn't require authentication)
function extractUserInfo(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    console.log('🔍 extractUserInfo middleware called');
    console.log('🔑 Authorization header:', authHeader ? 'Present' : 'Missing');
    console.log('🎫 Token extracted:', token ? 'Yes (length: ' + token.length + ')' : 'No');

    if (token) {
        jwt.verify(token, JWT_SECRET, (err, user) => {
            if (!err) {
                req.user = user;
                console.log('✅ Token verified successfully - User:', user.name, '(' + user.email + ')');
            } else {
                console.log('❌ Token verification failed:', err.message);
            }
        });
    } else {
        console.log('👤 No token provided - treating as guest');
    }
    next();
}

// API Routes

// Forgot Password - Request Password Reset
app.post('/api/forgot-password', authLimiter, async (req, res) => {
    try {
        const { email } = req.body;

        // Validate email
        if (!email || !validator.isEmail(email)) {
            return res.status(400).json({ error: 'Please provide a valid email address' });
        }

        // Check if user exists
        const user = await new Promise((resolve, reject) => {
            db.get('SELECT id, name, email FROM users WHERE email = ?', [email], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        // If user doesn't exist, don't reveal that to prevent email enumeration
        if (!user) {
            return res.json({ message: 'If an account with that email exists, a password reset link has been sent.' });
        }

        // Generate a reset token (valid for 1 hour)
        const resetToken = require('crypto').randomBytes(32).toString('hex');
        const expiresAt = new Date(Date.now() + 3600000); // 1 hour from now

        // Save the reset token to the database
        await new Promise((resolve, reject) => {
            db.run(
                'INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (?, ?, ?)',
                [user.id, resetToken, expiresAt.toISOString()],
                (err) => {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        // Send password reset email
        const resetUrl = `http://${req.headers.host}/Html pages/reset-password.html?token=${resetToken}`;

        try {
            await emailService.sendPasswordResetEmail(user.email, user.name, resetUrl);
            return res.json({ message: 'If an account with that email exists, a password reset link has been sent.' });
        } catch (emailError) {
            console.error('Error sending password reset email:', emailError);
            return res.status(500).json({ error: 'Failed to send password reset email' });
        }

    } catch (error) {
        console.error('Forgot password error:', error);
        return res.status(500).json({ error: 'An error occurred while processing your request' });
    }
});

// Reset Password
app.post('/api/reset-password', async (req, res) => {
    try {
        const { token, newPassword } = req.body;

        // Validate input
        if (!token || !newPassword) {
            return res.status(400).json({ error: 'Token and new password are required' });
        }

        if (newPassword.length < 8) {
            return res.status(400).json({ error: 'Password must be at least 8 characters long' });
        }

        // Find the reset token
        const resetToken = await new Promise((resolve, reject) => {
            db.get(
                'SELECT * FROM password_reset_tokens WHERE token = ? AND used = 0 AND expires_at > datetime("now")',
                [token],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (!resetToken) {
            return res.status(400).json({ error: 'Invalid or expired reset token' });
        }

        // Hash the new password
        const hashedPassword = await bcrypt.hash(newPassword, 10);

        // Update the user's password
        await new Promise((resolve, reject) => {
            db.run(
                'UPDATE users SET password = ? WHERE id = ?',
                [hashedPassword, resetToken.user_id],
                (err) => {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        // Invalidate all existing sessions for this user (security measure)
        await new Promise((resolve, reject) => {
            db.run(
                'DELETE FROM user_sessions WHERE user_id = ?',
                [resetToken.user_id],
                (err) => {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        // Mark the token as used
        await new Promise((resolve, reject) => {
            db.run(
                'UPDATE password_reset_tokens SET used = 1 WHERE id = ?',
                [resetToken.id],
                (err) => {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        return res.json({ message: 'Password has been reset successfully' });

    } catch (error) {
        console.error('Reset password error:', error);
        return res.status(500).json({ error: 'An error occurred while resetting your password' });
    }
});

// User Registration
app.post('/api/register', authLimiter, async (req, res) => {
    try {
        const { name, email, password, confirmPassword } = req.body;

        // Validation
        if (!validateName(name)) {
            return res.status(400).json({ error: 'Name must be between 2 and 100 characters' });
        }

        if (!validateEmail(email)) {
            return res.status(400).json({ error: 'Please provide a valid email address' });
        }

        if (!validatePassword(password)) {
            return res.status(400).json({ error: 'Password must be between 6 and 128 characters' });
        }

        if (password !== confirmPassword) {
            return res.status(400).json({ error: 'Passwords do not match' });
        }

        // Check if user already exists
        const existingUser = await new Promise((resolve, reject) => {
            db.get('SELECT id FROM users WHERE email = ?', [email.toLowerCase()], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (existingUser) {
            return res.status(400).json({ error: 'An account with this email already exists' });
        }

        // Hash password
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // Insert new user
        const result = await new Promise((resolve, reject) => {
            db.run(
                'INSERT INTO users (name, email, password) VALUES (?, ?, ?)',
                [name.trim(), email.toLowerCase(), hashedPassword],
                function (err) {
                    if (err) reject(err);
                    else resolve({ id: this.lastID });
                }
            );
        });

        // Send welcome email (don't wait for it to complete)
        emailService.sendWelcomeEmail(email.toLowerCase(), name.trim())
            .then(emailResult => {
                if (emailResult.success) {
                    console.log(`✅ Welcome email sent to ${email}`);
                } else {
                    console.error(`❌ Failed to send welcome email to ${email}:`, emailResult.error);
                }
            })
            .catch(emailError => {
                console.error(`❌ Welcome email error for ${email}:`, emailError);
            });

        res.status(201).json({
            success: true,
            message: 'Account created successfully! Check your email for a welcome message.',
            userId: result.id
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ error: 'Internal server error during registration' });
    }
});

// User Login
app.post('/api/login', authLimiter, async (req, res) => {
    const ipAddress = req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';

    try {
        const { email, password, rememberMe } = req.body;

        // Validation
        if (!validateEmail(email)) {
            // Log failed attempt - invalid email
            db.run(
                'INSERT INTO login_attempts (email, success, ip_address, user_agent, error_message) VALUES (?, ?, ?, ?, ?)',
                [email || 'invalid', false, ipAddress, userAgent, 'Invalid email format']
            );
            return res.status(400).json({ error: 'Please provide a valid email address' });
        }

        if (!password) {
            // Log failed attempt - missing password
            db.run(
                'INSERT INTO login_attempts (email, success, ip_address, user_agent, error_message) VALUES (?, ?, ?, ?, ?)',
                [email.toLowerCase(), false, ipAddress, userAgent, 'Password required']
            );
            return res.status(400).json({ error: 'Password is required' });
        }

        // Find user
        const user = await new Promise((resolve, reject) => {
            db.get(
                'SELECT id, name, email, password, is_active FROM users WHERE email = ?',
                [email.toLowerCase()],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (!user) {
            // Log failed attempt - user not found
            db.run(
                'INSERT INTO login_attempts (email, success, ip_address, user_agent, error_message) VALUES (?, ?, ?, ?, ?)',
                [email.toLowerCase(), false, ipAddress, userAgent, 'User not found']
            );
            return res.status(401).json({ error: 'Invalid email or password' });
        }

        if (!user.is_active) {
            // Log failed attempt - account deactivated
            db.run(
                'INSERT INTO login_attempts (email, success, ip_address, user_agent, error_message) VALUES (?, ?, ?, ?, ?)',
                [email.toLowerCase(), false, ipAddress, userAgent, 'Account deactivated']
            );
            return res.status(401).json({ error: 'Account is deactivated' });
        }

        // Verify password
        const isValidPassword = await bcrypt.compare(password, user.password);
        if (!isValidPassword) {
            // Log failed attempt - invalid password
            db.run(
                'INSERT INTO login_attempts (email, success, ip_address, user_agent, error_message) VALUES (?, ?, ?, ?, ?)',
                [email.toLowerCase(), false, ipAddress, userAgent, 'Invalid password']
            );
            return res.status(401).json({ error: 'Invalid email or password' });
        }

        // Generate JWT token
        const tokenExpiry = rememberMe ? '30d' : '24h';
        const token = jwt.sign(
            {
                userId: user.id,
                email: user.email,
                name: user.name
            },
            JWT_SECRET,
            { expiresIn: tokenExpiry }
        );

        // Update last login
        db.run(
            'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
            [user.id]
        );

        // Store session (optional, for logout functionality)
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + (rememberMe ? 30 : 1));

        db.run(
            'INSERT INTO user_sessions (user_id, token, expires_at) VALUES (?, ?, ?)',
            [user.id, token, expiresAt.toISOString()]
        );

        // Log successful login attempt
        db.run(
            'INSERT INTO login_attempts (email, success, ip_address, user_agent) VALUES (?, ?, ?, ?)',
            [email.toLowerCase(), true, ipAddress, userAgent]
        );

        res.json({
            success: true,
            message: 'Login successful',
            token: token,
            user: {
                id: user.id,
                name: user.name,
                email: user.email
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        // Log failed attempt - server error
        db.run(
            'INSERT INTO login_attempts (email, success, ip_address, user_agent, error_message) VALUES (?, ?, ?, ?, ?)',
            [req.body.email?.toLowerCase() || 'unknown', false, ipAddress, userAgent, 'Server error']
        );
        res.status(500).json({ error: 'Internal server error during login' });
    }
});

// Verify Token (for checking if user is still logged in)
app.get('/api/verify', authenticateToken, (req, res) => {
    res.json({
        success: true,
        user: {
            id: req.user.userId,
            name: req.user.name,
            email: req.user.email
        }
    });
});

// User Logout
app.post('/api/logout', authenticateToken, (req, res) => {
    const token = req.headers['authorization'].split(' ')[1];

    // Remove session from database
    db.run('DELETE FROM user_sessions WHERE token = ?', [token], (err) => {
        if (err) {
            console.error('Logout error:', err);
        }
    });

    res.json({ success: true, message: 'Logged out successfully' });
});

// Mass email endpoint
app.post('/api/admin/mass-email', authLimiter, async (req, res) => {
    try {
        const { userIds, subject, message } = req.body;

        // Validation
        if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
            return res.status(400).json({ error: 'Please select at least one user' });
        }

        if (!subject || subject.trim().length === 0) {
            return res.status(400).json({ error: 'Subject is required' });
        }

        if (!message || message.trim().length === 0) {
            return res.status(400).json({ error: 'Message is required' });
        }

        if (subject.length > 200) {
            return res.status(400).json({ error: 'Subject must be less than 200 characters' });
        }

        if (message.length > 5000) {
            return res.status(400).json({ error: 'Message must be less than 5000 characters' });
        }

        // Get user details for selected users
        const placeholders = userIds.map(() => '?').join(',');
        const users = await new Promise((resolve, reject) => {
            db.all(
                `SELECT id, name, email FROM users WHERE id IN (${placeholders}) AND is_active = 1`,
                userIds,
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                }
            );
        });

        if (users.length === 0) {
            return res.status(400).json({ error: 'No active users found for the selected IDs' });
        }

        console.log(`📧 Mass email request: ${users.length} recipients`);
        console.log(`📝 Subject: ${subject}`);

        // Send mass email
        const result = await emailService.sendMassEmail(users, subject.trim(), message.trim());

        if (result.success) {
            res.json({
                success: true,
                message: `Mass email sent successfully to ${result.totalSent} users`,
                totalSent: result.totalSent,
                totalFailed: result.totalFailed,
                details: result
            });
        } else {
            res.status(500).json({
                error: 'Failed to send mass email',
                details: result.error
            });
        }

    } catch (error) {
        console.error('Mass email error:', error);
        res.status(500).json({ error: 'Internal server error during mass email' });
    }
});

// Get User Profile
app.get('/api/profile', authenticateToken, async (req, res) => {
    try {
        const user = await new Promise((resolve, reject) => {
            db.get(
                'SELECT id, name, email, registration_date, last_login, email_verified FROM users WHERE id = ?',
                [req.user.userId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        res.json({
            success: true,
            user: user
        });

    } catch (error) {
        console.error('Profile error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Update User Profile
app.put('/api/profile', authenticateToken, async (req, res) => {
    try {
        const { name, email } = req.body;

        // Validation
        if (!validateName(name)) {
            return res.status(400).json({ error: 'Please provide a valid name (2-100 characters)' });
        }

        if (!validateEmail(email)) {
            return res.status(400).json({ error: 'Please provide a valid email address' });
        }

        // Check if email is already taken by another user
        const existingUser = await new Promise((resolve, reject) => {
            db.get('SELECT id FROM users WHERE email = ? AND id != ?', [email.toLowerCase(), req.user.userId], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (existingUser) {
            return res.status(400).json({ error: 'This email is already in use by another account' });
        }

        // Update user
        await new Promise((resolve, reject) => {
            db.run(
                'UPDATE users SET name = ?, email = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [name.trim(), email.toLowerCase(), req.user.userId],
                function (err) {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        res.json({
            success: true,
            message: 'Profile updated successfully'
        });

    } catch (error) {
        console.error('Profile update error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Change Password
app.post('/api/change-password', authenticateToken, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;

        // Validation
        if (!currentPassword || !newPassword) {
            return res.status(400).json({ error: 'Current password and new password are required' });
        }

        if (!validatePassword(newPassword)) {
            return res.status(400).json({ error: 'New password must be 6-128 characters long' });
        }

        // Get current user
        const user = await new Promise((resolve, reject) => {
            db.get('SELECT password FROM users WHERE id = ?', [req.user.userId], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Verify current password
        const isValidPassword = await bcrypt.compare(currentPassword, user.password);
        if (!isValidPassword) {
            return res.status(400).json({ error: 'Current password is incorrect' });
        }

        // Hash new password
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

        // Update password
        await new Promise((resolve, reject) => {
            db.run(
                'UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [hashedPassword, req.user.userId],
                function (err) {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        res.json({
            success: true,
            message: 'Password changed successfully'
        });

    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Delete Account
app.delete('/api/delete-account', authenticateToken, async (req, res) => {
    try {
        // Delete user sessions first (foreign key constraint)
        await new Promise((resolve, reject) => {
            db.run('DELETE FROM user_sessions WHERE user_id = ?', [req.user.userId], function (err) {
                if (err) reject(err);
                else resolve();
            });
        });

        // Delete user orders and related data
        await new Promise((resolve, reject) => {
            db.run('DELETE FROM order_items WHERE order_id IN (SELECT id FROM orders WHERE user_id = ?)', [req.user.userId], function (err) {
                if (err) reject(err);
                else resolve();
            });
        });

        await new Promise((resolve, reject) => {
            db.run('DELETE FROM payment_transactions WHERE order_id IN (SELECT id FROM orders WHERE user_id = ?)', [req.user.userId], function (err) {
                if (err) reject(err);
                else resolve();
            });
        });

        await new Promise((resolve, reject) => {
            db.run('DELETE FROM orders WHERE user_id = ?', [req.user.userId], function (err) {
                if (err) reject(err);
                else resolve();
            });
        });

        // Delete user
        await new Promise((resolve, reject) => {
            db.run('DELETE FROM users WHERE id = ?', [req.user.userId], function (err) {
                if (err) reject(err);
                else resolve();
            });
        });

        res.json({
            success: true,
            message: 'Account deleted successfully'
        });

    } catch (error) {
        console.error('Delete account error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Get User Orders
app.get('/api/orders', authenticateToken, async (req, res) => {
    try {
        const orders = await new Promise((resolve, reject) => {
            db.all(`
                SELECT
                    o.id,
                    o.order_number,
                    o.total_amount,
                    o.currency,
                    o.status,
                    o.payment_status,
                    o.payment_method,
                    o.created_at,
                    o.updated_at
                FROM orders o
                WHERE o.user_id = ?
                ORDER BY o.created_at DESC
            `, [req.user.userId], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        // Get order items for each order
        for (let order of orders) {
            const items = await new Promise((resolve, reject) => {
                db.all(`
                    SELECT
                        product_name,
                        product_type,
                        quantity,
                        unit_price,
                        total_price,
                        customization_data,
                        image_data
                    FROM order_items
                    WHERE order_id = ?
                `, [order.id], (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                });
            });
            order.items = items;
        }

        res.json({
            success: true,
            orders: orders
        });

    } catch (error) {
        console.error('Get orders error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Save Email Preferences
app.post('/api/email-preferences', authenticateToken, async (req, res) => {
    try {
        const { newsletter, orders, promotions } = req.body;

        // For now, we'll store preferences in a simple JSON format in the database
        // You could create a separate table for more complex preferences
        const preferences = JSON.stringify({ newsletter, orders, promotions });

        await new Promise((resolve, reject) => {
            db.run(
                'UPDATE users SET email_preferences = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [preferences, req.user.userId],
                function (err) {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        res.json({
            success: true,
            message: 'Email preferences saved successfully'
        });

    } catch (error) {
        console.error('Save email preferences error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Get Email Preferences
app.get('/api/email-preferences', authenticateToken, async (req, res) => {
    try {
        const user = await new Promise((resolve, reject) => {
            db.get(
                'SELECT email_preferences FROM users WHERE id = ?',
                [req.user.userId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        let preferences = { newsletter: true, orders: true, promotions: true }; // defaults
        if (user && user.email_preferences) {
            try {
                preferences = JSON.parse(user.email_preferences);
            } catch (e) {
                console.error('Error parsing email preferences:', e);
            }
        }

        res.json({
            success: true,
            preferences: preferences
        });

    } catch (error) {
        console.error('Get email preferences error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Wishlist API Endpoints

// Get user's wishlist
app.get('/api/wishlist', authenticateToken, async (req, res) => {
    try {
        const wishlistItems = await new Promise((resolve, reject) => {
            db.all(
                'SELECT product_id, created_at FROM user_wishlist WHERE user_id = ? ORDER BY created_at DESC',
                [req.user.userId],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                }
            );
        });

        res.json({
            success: true,
            wishlist: wishlistItems.map(item => item.product_id)
        });

    } catch (error) {
        console.error('Get wishlist error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Add item to wishlist
app.post('/api/wishlist', authenticateToken, async (req, res) => {
    try {
        const { productId } = req.body;

        if (!productId) {
            return res.status(400).json({ error: 'Product ID is required' });
        }

        // Check if item already exists in wishlist
        const existingItem = await new Promise((resolve, reject) => {
            db.get(
                'SELECT id FROM user_wishlist WHERE user_id = ? AND product_id = ?',
                [req.user.userId, productId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (existingItem) {
            return res.status(400).json({ error: 'Item already in wishlist' });
        }

        // Add to wishlist
        await new Promise((resolve, reject) => {
            db.run(
                'INSERT INTO user_wishlist (user_id, product_id) VALUES (?, ?)',
                [req.user.userId, productId],
                function (err) {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        res.json({
            success: true,
            message: 'Item added to wishlist'
        });

    } catch (error) {
        console.error('Add to wishlist error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Remove item from wishlist
app.delete('/api/wishlist/:productId', authenticateToken, async (req, res) => {
    try {
        const { productId } = req.params;

        await new Promise((resolve, reject) => {
            db.run(
                'DELETE FROM user_wishlist WHERE user_id = ? AND product_id = ?',
                [req.user.userId, productId],
                function (err) {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        res.json({
            success: true,
            message: 'Item removed from wishlist'
        });

    } catch (error) {
        console.error('Remove from wishlist error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Temporary order storage (in production, use Redis or database)
const tempOrders = new Map();

// Temporary image storage for Stripe checkout (in production, use CDN)
const tempImages = new Map();

// Clean up expired temporary orders and images every 5 minutes
setInterval(() => {
    const now = Date.now();
    for (const [key, value] of tempOrders.entries()) {
        if (value.expiresAt < now) {
            tempOrders.delete(key);
        }
    }
    for (const [key, value] of tempImages.entries()) {
        if (value.expiresAt < now) {
            tempImages.delete(key);
        }
    }
}, 5 * 60 * 1000);

// Serve temporary images for Stripe checkout
app.get('/api/temp-image/:imageId', (req, res) => {
    const { imageId } = req.params;
    const imageData = tempImages.get(imageId);

    if (!imageData || imageData.expiresAt < Date.now()) {
        return res.status(404).json({ error: 'Image not found or expired' });
    }

    // Parse the base64 data
    const matches = imageData.data.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);
    if (!matches) {
        return res.status(400).json({ error: 'Invalid image data' });
    }

    const imageType = matches[1];
    const base64Data = matches[2];
    const buffer = Buffer.from(base64Data, 'base64');

    res.set({
        'Content-Type': `image/${imageType}`,
        'Content-Length': buffer.length,
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
    });

    res.send(buffer);
});

// Send checkout notification email to business owner
app.post('/api/send-checkout-notification', extractUserInfo, async (req, res) => {
    console.log('🔔 Checkout notification endpoint called');
    console.log('📦 Request body:', JSON.stringify(req.body, null, 2));

    try {
        const { orderData, total } = req.body;

        console.log('🔍 Validating order data...');
        if (!orderData || !orderData.cart || orderData.cart.length === 0) {
            console.log('❌ Invalid order data received');
            return res.status(400).json({ error: 'Invalid order data' });
        }

        console.log(`✅ Order data valid - ${orderData.cart.length} items, total: €${total}`);

        // Generate a temporary order number for the notification
        const tempOrderNumber = `TEMP-${Date.now()}`;
        console.log(`📋 Generated temp order number: ${tempOrderNumber}`);

        // Extract user info if available
        const userInfo = req.user ? {
            name: req.user.name,
            email: req.user.email,
            userId: req.user.userId
        } : null;

        // Send notification email to business owner
        console.log('📧 Sending checkout notification email...');
        if (userInfo) {
            console.log(`👤 User logged in: ${userInfo.name} (${userInfo.email})`);
        } else {
            console.log('👤 Guest checkout (no user logged in)');
        }

        await emailService.sendCheckoutNotification(
            tempOrderNumber,
            orderData,
            total,
            userInfo
        );

        console.log('✅ Checkout notification email sent successfully');
        res.json({
            success: true,
            message: 'Checkout notification sent successfully',
            orderNumber: tempOrderNumber
        });

    } catch (error) {
        console.error('❌ Failed to send checkout notification:', error);
        res.status(500).json({
            error: 'Failed to send notification',
            details: error.message
        });
    }
});

// Create Stripe Checkout Session
app.post('/api/create-checkout-session', extractUserInfo, async (req, res) => {
    try {
        // Check if Stripe is properly configured
        if (!isConfigured) {
            return res.status(400).json({
                error: 'Payment system not configured',
                message: 'Stripe API keys are not set up. Please configure your .env file.'
            });
        }

        const { amount, currency = 'eur', orderData, successUrl, cancelUrl } = req.body;

        // Validate amount
        if (!amount || amount < 50) { // Minimum 50 cents
            return res.status(400).json({ error: 'Invalid amount' });
        }

        console.log(`💳 Creating ${isLiveMode ? 'LIVE' : 'TEST'} payment session for $${(amount / 100).toFixed(2)}`);

        // Generate a temporary order ID
        const tempOrderId = 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        // Store order data temporarily (expires in 30 minutes)
        tempOrders.set(tempOrderId, {
            orderData,
            createdAt: Date.now(),
            expiresAt: Date.now() + (30 * 60 * 1000) // 30 minutes
        });

        // Create line items from cart data
        const lineItems = orderData.cart.map((item, index) => {
            const productData = {
                name: item.name || 'Custom Magnet',
                description: `${item.magnetColor} magnet with custom design - Language: ${item.language === 'en' ? 'English' : 'Maltese'}, Size: ${item.size}`,
            };

            // Add image if available and it's a valid data URL
            if (item.image && item.image.startsWith('data:image/')) {
                try {
                    // Generate a unique image ID
                    const imageId = `${tempOrderId}_item_${index}_${Date.now()}`;

                    // Store the image temporarily (expires in 2 hours)
                    tempImages.set(imageId, {
                        data: item.image,
                        createdAt: Date.now(),
                        expiresAt: Date.now() + (2 * 60 * 60 * 1000) // 2 hours
                    });

                    // Create the temporary image URL
                    const imageUrl = `${req.protocol}://${req.get('host')}/api/temp-image/${imageId}`;
                    productData.images = [imageUrl];

                    console.log(`📸 Image stored for ${item.name || 'Custom Magnet'} - URL: ${imageUrl}`);
                } catch (error) {
                    console.warn('Error processing image for Stripe:', error);
                    productData.description += ' (Custom image included)';
                }
            }

            return {
                price_data: {
                    currency: currency,
                    product_data: productData,
                    unit_amount: Math.round(parseFloat(item.total) * 100 / item.quantity), // Convert to cents per unit
                },
                quantity: item.quantity,
            };
        });

        // Add shipping as a separate line item
        if (orderData.totals.shipping > 0) {
            lineItems.push({
                price_data: {
                    currency: currency,
                    product_data: {
                        name: 'Shipping',
                        description: 'Standard shipping',
                    },
                    unit_amount: Math.round(orderData.totals.shipping * 100), // Convert to cents
                },
                quantity: 1,
            });
        }

        // Create Stripe Checkout Session configuration
        const sessionConfig = {
            payment_method_types: ['card'],
            line_items: lineItems,
            mode: 'payment',
            success_url: successUrl + '?session_id={CHECKOUT_SESSION_ID}',
            cancel_url: cancelUrl,
            billing_address_collection: 'required',
            shipping_address_collection: {
                allowed_countries: ['US', 'CA', 'GB', 'AU', 'MT', 'DE', 'FR', 'IT', 'ES', 'NL'], // Add more countries as needed
            },
            payment_intent_data: {
                statement_descriptor: 'WITH LOVE MAGNETS',
                statement_descriptor_suffix: 'HANDMADE',
            },
            metadata: {
                tempOrderId: tempOrderId,
                customerEmail: orderData.customerInfo.email || 'collected-by-stripe',
                orderTotal: (amount / 100).toString(),
                itemCount: orderData.cart.length.toString(),
                // Add user information if available
                ...(req.user ? {
                    userId: req.user.userId.toString(),
                    userName: req.user.name,
                    userEmail: req.user.email,
                    isLoggedIn: 'true'
                } : {
                    isLoggedIn: 'false'
                })
            },
            expires_at: Math.floor(Date.now() / 1000) + (30 * 60), // Expires in 30 minutes
            automatic_tax: {
                enabled: false, // Set to true if you want Stripe to calculate taxes
            },
            custom_text: {
                submit: {
                    message: 'Complete your order with With Love Handmade Gifts'
                }
            },
        };

        // Add customer_email only if provided
        if (orderData.customerInfo.email && orderData.customerInfo.email.trim() !== '') {
            sessionConfig.customer_email = orderData.customerInfo.email;
        }

        // Create Stripe Checkout Session
        const session = await stripe.checkout.sessions.create(sessionConfig);

        res.json({
            success: true,
            sessionId: session.id,
            url: session.url
        });

    } catch (error) {
        console.error('Stripe checkout session error:', error);
        res.status(500).json({
            error: 'Failed to create checkout session',
            details: error.message
        });
    }
});

// Verify Stripe Payment
app.post('/api/verify-payment', async (req, res) => {
    try {
        const { sessionId } = req.body;

        if (!sessionId) {
            return res.status(400).json({ error: 'Session ID is required' });
        }

        // Retrieve the session from Stripe
        const session = await stripe.checkout.sessions.retrieve(sessionId);

        if (session.payment_status === 'paid') {
            // Payment was successful
            const tempOrderId = session.metadata.tempOrderId;
            const tempOrder = tempOrders.get(tempOrderId);

            if (!tempOrder) {
                return res.json({
                    success: false,
                    error: 'Order data not found or expired'
                });
            }

            const orderData = tempOrder.orderData;

            // Extract user information from session metadata
            const userInfo = session.metadata.isLoggedIn === 'true' ? {
                userId: parseInt(session.metadata.userId),
                name: session.metadata.userName,
                email: session.metadata.userEmail
            } : null;

            // Clean up temporary storage
            tempOrders.delete(tempOrderId);

            console.log(`✅ Payment confirmed for $${(session.amount_total / 100).toFixed(2)} - ${isLiveMode ? 'REAL MONEY' : 'TEST'}`);
            if (userInfo) {
                console.log(`👤 Order placed by logged-in user: ${userInfo.name} (${userInfo.email})`);
            } else {
                console.log('👤 Order placed by guest customer');
            }

            try {
                // 1. Save the order to database
                const orderNumber = 'ORD-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5).toUpperCase();

                // Insert order
                const orderResult = await new Promise((resolve, reject) => {
                    db.run(`
                        INSERT INTO orders (
                            order_number, user_id, total_amount, currency, status,
                            payment_status, payment_method, customer_email, customer_name,
                            shipping_address, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    `, [
                        orderNumber,
                        userInfo ? userInfo.userId : null, // Include user ID if logged in
                        session.amount_total / 100,
                        session.currency,
                        'confirmed',
                        'paid',
                        'stripe',
                        orderData.customerInfo.email,
                        `${orderData.customerInfo.firstName} ${orderData.customerInfo.lastName}`,
                        JSON.stringify(orderData.customerInfo)
                    ], function (err) {
                        if (err) reject(err);
                        else resolve(this.lastID);
                    });
                });

                // 2. Save order items
                for (const item of orderData.cart) {
                    await new Promise((resolve, reject) => {
                        db.run(`
                            INSERT INTO order_items (
                                order_id, product_name, product_type, quantity,
                                unit_price, total_price, customization_data, image_data
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        `, [
                            orderResult,
                            item.name || 'Custom Magnet',
                            'magnet',
                            item.quantity,
                            parseFloat(item.total) / item.quantity,
                            parseFloat(item.total),
                            JSON.stringify({
                                magnetColor: item.magnetColor,
                                textColor: item.textColor,
                                language: item.language,
                                text: item.text
                            }),
                            item.image || null
                        ], function (err) {
                            if (err) reject(err);
                            else resolve();
                        });
                    });
                }

                // 3. Save payment transaction
                await new Promise((resolve, reject) => {
                    db.run(`
                        INSERT INTO payment_transactions (
                            order_id, stripe_session_id, amount, currency,
                            status, created_at
                        ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    `, [
                        orderResult,
                        sessionId,
                        session.amount_total / 100,
                        session.currency,
                        'completed'
                    ], function (err) {
                        if (err) reject(err);
                        else resolve();
                    });
                });

                // 4. Send confirmation emails (if email service is configured)
                try {
                    // Send confirmation email to customer
                    await emailService.sendOrderConfirmation(
                        orderData.customerInfo.email,
                        orderData.customerInfo.firstName,
                        orderNumber,
                        orderData,
                        userInfo
                    );
                    console.log(`📧 Order confirmation email sent to ${orderData.customerInfo.email}`);

                    // Send notification email to business owner
                    await emailService.sendOwnerNotification(
                        orderNumber,
                        orderData,
                        session.amount_total / 100,
                        userInfo
                    );
                    console.log(`📧 Owner notification email <NAME_EMAIL>`);

                } catch (emailError) {
                    console.error('Email sending failed:', emailError);
                    // Don't fail the order if email fails
                }

                res.json({
                    success: true,
                    orderData: orderData,
                    orderNumber: orderNumber,
                    paymentStatus: session.payment_status,
                    amountTotal: session.amount_total / 100,
                    currency: session.currency,
                    isLivePayment: isLiveMode
                });

            } catch (dbError) {
                console.error('Database error saving order:', dbError);
                // Payment succeeded but order saving failed
                res.json({
                    success: true,
                    orderData: orderData,
                    paymentStatus: session.payment_status,
                    amountTotal: session.amount_total / 100,
                    currency: session.currency,
                    warning: 'Payment processed but order recording failed. Please contact support.',
                    isLivePayment: isLiveMode
                });
            }
        } else {
            res.json({
                success: false,
                error: 'Payment not completed',
                paymentStatus: session.payment_status
            });
        }

    } catch (error) {
        console.error('Payment verification error:', error);
        res.status(500).json({
            error: 'Failed to verify payment',
            details: error.message
        });
    }
});

// Test Stripe Connection
app.get('/api/test-stripe', async (req, res) => {
    try {
        // Test if Stripe is working by retrieving account info
        const account = await stripe.accounts.retrieve();
        res.json({
            success: true,
            message: 'Stripe connection successful!',
            accountId: account.id,
            country: account.country,
            currency: account.default_currency,
            isTestMode: isTestMode,
            isLiveMode: isLiveMode
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            type: error.type
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Admin panel for viewing users (simple web interface)
app.get('/admin/users', async (req, res) => {
    try {
        const users = await new Promise((resolve, reject) => {
            db.all(`
                SELECT id, name, email, registration_date, last_login, is_active, email_verified
                FROM users
                ORDER BY registration_date DESC
            `, [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        const sessionStats = await new Promise((resolve, reject) => {
            db.get(`
                SELECT
                    COUNT(*) as total_sessions,
                    COUNT(CASE WHEN expires_at > datetime('now') THEN 1 END) as active_sessions
                FROM user_sessions
            `, [], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        const loggedInUsers = await new Promise((resolve, reject) => {
            db.all(`
                SELECT DISTINCT u.id, u.name, u.email, u.last_login, s.created_at as session_start
                FROM users u
                INNER JOIN user_sessions s ON u.id = s.user_id
                WHERE s.expires_at > datetime('now')
                ORDER BY s.created_at DESC
            `, [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        const newsletterSubscribers = await new Promise((resolve, reject) => {
            db.all(`
                SELECT id, email, subscribed_at, is_active, source
                FROM newsletter_subscribers
                WHERE is_active = 1
                ORDER BY subscribed_at DESC
            `, [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        const html = generateAdminHTML(users, sessionStats, loggedInUsers, newsletterSubscribers);
        res.send(html);
    } catch (error) {
        console.error('Admin panel error:', error);
        res.status(500).send('Error loading admin panel');
    }
});

// Generate admin panel HTML
function generateAdminHTML(users, sessionStats, loggedInUsers, newsletterSubscribers) {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>User Management - Julia's Magnet Website</title>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #a67f83; text-align: center; margin-bottom: 30px; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
            .stat-card { background: #f0d9d9; padding: 20px; border-radius: 8px; text-align: center; }
            .stat-number { font-size: 2em; font-weight: bold; color: #a67f83; }
            .stat-label { color: #666; margin-top: 5px; }

            /* Section Styles */
            .section { margin-bottom: 40px; background: white; border-radius: 12px; padding: 25px; box-shadow: 0 3px 15px rgba(0,0,0,0.08); border: 1px solid #f0f0f0; }
            .section h2 { color: #a67f83; border-bottom: 2px solid #f0d9d9; padding-bottom: 15px; margin-bottom: 25px; font-size: 1.4em; }
            .section h2 i { margin-right: 12px; color: #ff6b8b; }
            .status.active { background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; }

            /* Enhanced Button Styles */
            .action-buttons { display: flex; gap: 15px; margin: 15px 0; flex-wrap: wrap; align-items: center; }
            .action-btn {
                background: linear-gradient(135deg, #ff6b8b, #ff8fab);
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 14px;
            }
            .action-btn:hover {
                background: linear-gradient(135deg, #ff5577, #ff7799);
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(255, 107, 139, 0.3);
            }
            .action-btn:disabled {
                background: #ccc;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }
            .action-btn.secondary {
                background: linear-gradient(135deg, #6c757d, #868e96);
            }
            .action-btn.secondary:hover {
                background: linear-gradient(135deg, #5a6268, #6c757d);
            }
            .action-btn.secondary.active {
                background: linear-gradient(135deg, #28a745, #34ce57);
                color: white;
            }
            .action-btn.secondary.active:hover {
                background: linear-gradient(135deg, #218838, #28a745);
            }

            /* Selection Controls Layout */
            .selection-controls {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 8px;
                flex-wrap: wrap;
                gap: 15px;
            }

            .select-all-container {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .selected-count {
                color: #666;
                font-size: 14px;
                font-style: italic;
            }

            /* Table Styles */
            .table-container { margin-top: 20px; }
            table { width: 100%; border-collapse: collapse; }
            th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background: #a67f83; color: white; position: sticky; top: 0; }
            tr:hover { background: #f9f9f9; }
            .status { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; }
            .active { background: #d4edda; color: #155724; }
            .inactive { background: #f8d7da; color: #721c24; }
            .verified { color: #28a745; }
            .unverified { color: #dc3545; }

            /* Controls */
            .controls { margin: 20px 0; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px; }
            .back-link { color: #a67f83; text-decoration: none; }
            .back-link:hover { text-decoration: underline; }
            .refresh-btn { background: #a67f83; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
            .refresh-btn:hover { background: #8c6b6e; }

            /* Selection Controls */
            .selection-controls {
                display: flex;
                gap: 15px;
                align-items: center;
                margin: 20px 0;
                padding: 20px;
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                border-radius: 12px;
                border: 1px solid #dee2e6;
                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            }
            .select-all-container {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 10px 15px;
                background: white;
                border-radius: 8px;
                border: 2px solid #e9ecef;
                transition: all 0.3s ease;
            }
            .select-all-container:hover {
                border-color: #a67f83;
                box-shadow: 0 2px 8px rgba(166, 127, 131, 0.15);
            }
            .select-all-container label {
                margin: 0;
                font-weight: 600;
                color: #495057;
                cursor: pointer;
                user-select: none;
            }
            .mass-email-btn {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 0.95em;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 8px;
                transition: all 0.3s ease;
                box-shadow: 0 3px 12px rgba(0, 123, 255, 0.3);
            }
            .mass-email-btn:hover:not(:disabled) {
                background: linear-gradient(135deg, #0056b3, #004085);
                transform: translateY(-2px);
                box-shadow: 0 5px 20px rgba(0, 123, 255, 0.4);
            }
            .mass-email-btn:disabled {
                background: #6c757d;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }
            .selected-count {
                color: #495057;
                font-weight: 600;
                padding: 10px 15px;
                background: white;
                border-radius: 8px;
                border: 2px solid #e9ecef;
                margin-left: auto;
            }

            /* Mass Email Modal */
            .modal {
                display: none;
                position: fixed;
                z-index: 1000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.6);
                backdrop-filter: blur(3px);
                animation: fadeIn 0.3s ease;
            }
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            .modal-content {
                background-color: white;
                margin: 3% auto;
                padding: 0;
                border-radius: 16px;
                width: 90%;
                max-width: 650px;
                max-height: 85vh;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                animation: slideIn 0.3s ease;
            }
            @keyframes slideIn {
                from { transform: translateY(-50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 25px 30px;
                background: linear-gradient(135deg, #a67f83, #8c6b6e);
                color: white;
                border-radius: 16px 16px 0 0;
            }
            .modal-title {
                color: white;
                margin: 0;
                font-size: 1.4em;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .close {
                color: rgba(255,255,255,0.8);
                font-size: 28px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                padding: 5px;
                border-radius: 50%;
            }
            .close:hover {
                color: white;
                background: rgba(255,255,255,0.1);
                transform: rotate(90deg);
            }

            /* Form Styles */
            .modal-body {
                padding: 30px;
                max-height: 60vh;
                overflow-y: auto;
            }
            .form-group {
                margin-bottom: 25px;
            }
            .form-group label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #495057;
                font-size: 0.95em;
            }
            .form-group input, .form-group textarea {
                width: 100%;
                padding: 12px 16px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 14px;
                box-sizing: border-box;
                transition: all 0.3s ease;
                font-family: inherit;
            }
            .form-group input:focus, .form-group textarea:focus {
                outline: none;
                border-color: #a67f83;
                box-shadow: 0 0 0 3px rgba(166, 127, 131, 0.1);
            }
            .form-group textarea {
                height: 140px;
                resize: vertical;
                line-height: 1.5;
            }
            .form-actions {
                display: flex;
                gap: 12px;
                justify-content: flex-end;
                padding: 25px 0 0 0;
                margin-top: 25px;
                border-top: 1px solid #e9ecef;
            }
            .btn-primary {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 8px;
                transition: all 0.3s ease;
                box-shadow: 0 3px 12px rgba(0, 123, 255, 0.3);
            }
            .btn-primary:hover {
                background: linear-gradient(135deg, #0056b3, #004085);
                transform: translateY(-2px);
                box-shadow: 0 5px 20px rgba(0, 123, 255, 0.4);
            }
            .btn-secondary {
                background: #6c757d;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                transition: all 0.3s ease;
            }
            .btn-secondary:hover {
                background: #545b62;
                transform: translateY(-1px);
            }

            /* Recipients List */
            .recipients-list {
                margin-bottom: 25px;
                padding: 20px;
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                border-radius: 12px;
                border: 2px solid #dee2e6;
            }
            .recipients-list strong {
                color: #495057;
                font-size: 1.1em;
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 15px;
            }
            .recipients-display {
                display: grid;
                gap: 8px;
            }
            .recipient-item {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 10px 15px;
                background: white;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                font-size: 0.9em;
            }
            .recipient-item i {
                color: #a67f83;
            }

            /* Recipient Tags */
            .recipient-tag {
                display: inline-block;
                background: linear-gradient(135deg, #e3f2fd, #bbdefb);
                color: #1976d2;
                padding: 6px 12px;
                margin: 4px;
                border-radius: 20px;
                font-size: 13px;
                font-weight: 500;
                border: 1px solid #90caf9;
            }

            /* Checkbox Styles */
            .user-checkbox {
                transform: scale(1.3);
                margin-right: 8px;
                accent-color: #a67f83;
                cursor: pointer;
            }
            .select-all-checkbox {
                transform: scale(1.4);
                margin-right: 8px;
                accent-color: #a67f83;
                cursor: pointer;
            }

            /* Loading and Messages */
            .loading {
                display: none;
                text-align: center;
                padding: 30px;
                color: #666;
                font-size: 1.1em;
            }
            .loading i {
                font-size: 2em;
                color: #a67f83;
                margin-bottom: 15px;
                display: block;
            }
            .message {
                padding: 18px 20px;
                border-radius: 10px;
                margin: 20px 0;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 10px;
                animation: slideDown 0.3s ease;
            }
            @keyframes slideDown {
                from { transform: translateY(-10px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            .message.success {
                background: linear-gradient(135deg, #d4edda, #c3e6cb);
                color: #155724;
                border: 2px solid #c3e6cb;
                box-shadow: 0 3px 12px rgba(21, 87, 36, 0.1);
            }
            .message.error {
                background: linear-gradient(135deg, #f8d7da, #f5c6cb);
                color: #721c24;
                border: 2px solid #f5c6cb;
                box-shadow: 0 3px 12px rgba(114, 28, 36, 0.1);
            }
            .message i {
                font-size: 1.2em;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="controls">
                <a href="/" class="back-link"><i class="fas fa-arrow-left"></i> Back to Website</a>
                <button class="refresh-btn" onclick="location.reload()"><i class="fas fa-sync"></i> Refresh</button>
            </div>

            <h1><i class="fas fa-users"></i> User Management Dashboard</h1>

            <div id="messageContainer"></div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">${users.length}</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${users.filter(u => u.is_active).length}</div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${users.filter(u => u.email_verified).length}</div>
                    <div class="stat-label">Verified Emails</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${sessionStats.active_sessions}</div>
                    <div class="stat-label">Active Sessions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${newsletterSubscribers.length}</div>
                    <div class="stat-label">Newsletter Subscribers</div>
                </div>
            </div>

            <!-- All Registered Users Section -->
            <div class="section">
                <h2><i class="fas fa-users"></i> All Registered Users (${users.length})</h2>
                ${users.length === 0 ?
            '<p style="text-align: center; color: #666; font-style: italic;">No users registered yet. Users will appear here after they sign up on your website.</p>' :
            `<div class="selection-controls">
                    <div class="select-all-container">
                        <input type="checkbox" id="selectAll" class="select-all-checkbox">
                        <label for="selectAll">Select All Users</label>
                    </div>
                    <div class="action-buttons">
                        <button id="massEmailBtn" class="action-btn" disabled>
                            <i class="fas fa-envelope"></i> Send Mass Email
                        </button>
                        <button id="allUsersBtn" class="action-btn secondary active">
                            <i class="fas fa-users"></i> All Users
                        </button>
                        <button id="subscribedUsersBtn" class="action-btn secondary">
                            <i class="fas fa-envelope-open"></i> Subscribed Users (${newsletterSubscribers.length})
                        </button>
                    </div>
                    <span id="selectedCount" class="selected-count">0 users selected</span>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Select</th>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Registered</th>
                                <th>Last Login</th>
                                <th>Status</th>
                                <th>Verified</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${users.map(user => `
                                <tr>
                                    <td>
                                        <input type="checkbox" class="user-checkbox" value="${user.id}"
                                               data-name="${user.name}" data-email="${user.email}"
                                               ${user.is_active ? '' : 'disabled'}>
                                    </td>
                                    <td>${user.id}</td>
                                    <td>${user.name}</td>
                                    <td>${user.email}</td>
                                    <td>${new Date(user.registration_date).toLocaleDateString()}</td>
                                    <td>${user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}</td>
                                    <td><span class="status ${user.is_active ? 'active' : 'inactive'}">${user.is_active ? 'Active' : 'Inactive'}</span></td>
                                    <td><i class="fas ${user.email_verified ? 'fa-check verified' : 'fa-times unverified'}"></i></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>`
        }

            <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; font-size: 0.9em; color: #666;">
                <strong>💡 Tips:</strong><br>
                • This page shows all registered users from your database<br>
                • Select users and send mass emails to communicate with your community<br>
                • Only active users can be selected for mass emails<br>
                • Refresh the page to see new registrations<br>
                • Use command line tools for advanced management: <code>npm run view-users</code> or <code>npm run user-stats</code>
            </div>
        </div>

        <!-- Mass Email Modal -->
        <div id="massEmailModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 class="modal-title"><i class="fas fa-envelope"></i> Send Mass Email</h2>
                    <span class="close">&times;</span>
                </div>

                <div class="modal-body">
                    <div id="recipientsList" class="recipients-list">
                        <strong><i class="fas fa-users"></i> Recipients:</strong>
                        <div id="recipientsDisplay" class="recipients-display"></div>
                    </div>

                    <form id="massEmailForm">
                        <div class="form-group">
                            <label for="emailSubject"><i class="fas fa-tag"></i> Subject *</label>
                            <input type="text" id="emailSubject" name="subject" required maxlength="200"
                                   placeholder="Enter email subject...">
                        </div>

                        <div class="form-group">
                            <label for="emailMessage"><i class="fas fa-edit"></i> Message *</label>
                            <textarea id="emailMessage" name="message" required maxlength="5000"
                                      placeholder="Write your message here..."></textarea>
                        </div>
                    </form>

                    <div id="emailLoading" class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        Sending emails...
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-secondary" id="cancelEmail">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                        <button type="submit" class="btn-primary" form="massEmailForm">
                            <i class="fas fa-paper-plane"></i> Send Email
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Global variables
            let selectedUsers = [];

            // DOM elements
            const selectAllCheckbox = document.getElementById('selectAll');
            const userCheckboxes = document.querySelectorAll('.user-checkbox');
            const massEmailBtn = document.getElementById('massEmailBtn');
            const selectedCountSpan = document.getElementById('selectedCount');
            const modal = document.getElementById('massEmailModal');
            const closeModal = document.querySelector('.close');
            const cancelBtn = document.getElementById('cancelEmail');
            const massEmailForm = document.getElementById('massEmailForm');
            const emailLoading = document.getElementById('emailLoading');
            const messageContainer = document.getElementById('messageContainer');

            // Initialize
            updateSelectedCount();

            // Select All functionality
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                userCheckboxes.forEach(checkbox => {
                    if (!checkbox.disabled) {
                        checkbox.checked = isChecked;
                    }
                });
                updateSelectedUsers();
            });

            // Individual checkbox change
            userCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedUsers);
            });

            // Update selected users and UI
            function updateSelectedUsers() {
                selectedUsers = [];
                let checkedCount = 0;
                let enabledCount = 0;

                userCheckboxes.forEach(checkbox => {
                    if (!checkbox.disabled) {
                        enabledCount++;
                        if (checkbox.checked) {
                            checkedCount++;
                            selectedUsers.push({
                                id: checkbox.value,
                                name: checkbox.dataset.name,
                                email: checkbox.dataset.email
                            });
                        }
                    }
                });

                // Update select all checkbox state
                if (checkedCount === 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = false;
                } else if (checkedCount === enabledCount) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = true;
                } else {
                    selectAllCheckbox.indeterminate = true;
                }

                updateSelectedCount();
            }

            // Update selected count display
            function updateSelectedCount() {
                const count = selectedUsers.length;
                selectedCountSpan.textContent = count + ' user' + (count !== 1 ? 's' : '') + ' selected';
                massEmailBtn.disabled = count === 0;
            }

            // Show mass email modal
            massEmailBtn.addEventListener('click', function() {
                if (selectedUsers.length === 0) return;

                // Display recipients
                const recipientsDisplay = document.getElementById('recipientsDisplay');
                recipientsDisplay.innerHTML = selectedUsers.map(user =>
                    '<div class="recipient-item"><i class="fas fa-user"></i> <strong>' + user.name + '</strong> <span style="color: #666;">(' + user.email + ')</span></div>'
                ).join('');

                modal.style.display = 'block';
            });

            // Close modal
            function closeEmailModal() {
                modal.style.display = 'none';
                massEmailForm.reset();
                emailLoading.style.display = 'none';
            }

            closeModal.addEventListener('click', closeEmailModal);
            cancelBtn.addEventListener('click', closeEmailModal);

            // Close modal when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    closeEmailModal();
                }
            });

            // Handle form submission
            massEmailForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const subject = document.getElementById('emailSubject').value.trim();
                const message = document.getElementById('emailMessage').value.trim();

                if (!subject || !message) {
                    showMessage('Please fill in both subject and message', 'error');
                    return;
                }

                if (selectedUsers.length === 0) {
                    showMessage('No users selected', 'error');
                    return;
                }

                // Show loading
                emailLoading.style.display = 'block';

                try {
                    const response = await fetch('/api/admin/mass-email', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            userIds: selectedUsers.map(u => u.id),
                            subject: subject,
                            message: message
                        })
                    });

                    const result = await response.json();

                    if (response.ok && result.success) {
                        showMessage(result.message, 'success');
                        closeEmailModal();

                        // Clear selections
                        userCheckboxes.forEach(cb => cb.checked = false);
                        updateSelectedUsers();
                    } else {
                        showMessage(result.error || 'Failed to send mass email', 'error');
                    }
                } catch (error) {
                    showMessage('Network error: ' + error.message, 'error');
                } finally {
                    emailLoading.style.display = 'none';
                }
            });

            // Show message function
            function showMessage(text, type) {
                const icon = type === 'success' ? '<i class="fas fa-check-circle"></i>' : '<i class="fas fa-exclamation-triangle"></i>';
                messageContainer.innerHTML = '<div class="message ' + type + '">' + icon + text + '</div>';
                setTimeout(() => {
                    messageContainer.innerHTML = '';
                }, 5000);
            }

            // Logged-in Users functionality
            const loggedInCheckboxes = document.querySelectorAll('.logged-in-user-checkbox');
            const selectAllLoggedInBtn = document.getElementById('selectAllLoggedInBtn');
            const selectAllLoggedInCheckbox = document.getElementById('selectAllLoggedInCheckbox');
            const massEmailLoggedInBtn = document.getElementById('massEmailLoggedInBtn');
            const loggedInSelectedCount = document.getElementById('loggedInSelectedCount');
            let selectedLoggedInUsers = [];

            if (loggedInCheckboxes.length > 0) {
                // Select all logged-in users
                selectAllLoggedInBtn?.addEventListener('click', function() {
                    const allChecked = Array.from(loggedInCheckboxes).every(cb => cb.checked);
                    loggedInCheckboxes.forEach(checkbox => {
                        checkbox.checked = !allChecked;
                    });
                    selectAllLoggedInCheckbox.checked = !allChecked;
                    updateSelectedLoggedInUsers();
                });

                selectAllLoggedInCheckbox?.addEventListener('change', function() {
                    loggedInCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateSelectedLoggedInUsers();
                });

                // Individual checkbox change
                loggedInCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', updateSelectedLoggedInUsers);
                });

                // Mass email for logged-in users
                massEmailLoggedInBtn?.addEventListener('click', function() {
                    if (selectedLoggedInUsers.length === 0) return;
                    openMassEmailModal(selectedLoggedInUsers, 'logged-in users');
                });

                function updateSelectedLoggedInUsers() {
                    selectedLoggedInUsers = [];
                    loggedInCheckboxes.forEach(checkbox => {
                        if (checkbox.checked) {
                            selectedLoggedInUsers.push({
                                id: checkbox.dataset.userId,
                                email: checkbox.dataset.userEmail,
                                name: checkbox.dataset.userName
                            });
                        }
                    });

                    const count = selectedLoggedInUsers.length;
                    loggedInSelectedCount.textContent = count + ' user' + (count !== 1 ? 's' : '') + ' selected';
                    massEmailLoggedInBtn.disabled = count === 0;

                    // Update select all checkbox state
                    selectAllLoggedInCheckbox.checked = count === loggedInCheckboxes.length;
                    selectAllLoggedInCheckbox.indeterminate = count > 0 && count < loggedInCheckboxes.length;
                }

                updateSelectedLoggedInUsers();
            }

            // Newsletter Subscribers functionality
            const subscriberCheckboxes = document.querySelectorAll('.subscriber-checkbox');
            const selectAllSubscribersBtn = document.getElementById('selectAllSubscribersBtn');
            const selectAllSubscribersCheckbox = document.getElementById('selectAllSubscribersCheckbox');
            const massEmailSubscribersBtn = document.getElementById('massEmailSubscribersBtn');
            const subscribersSelectedCount = document.getElementById('subscribersSelectedCount');
            let selectedSubscribers = [];

            if (subscriberCheckboxes.length > 0) {
                // Select all subscribers
                selectAllSubscribersBtn?.addEventListener('click', function() {
                    const allChecked = Array.from(subscriberCheckboxes).every(cb => cb.checked);
                    subscriberCheckboxes.forEach(checkbox => {
                        checkbox.checked = !allChecked;
                    });
                    selectAllSubscribersCheckbox.checked = !allChecked;
                    updateSelectedSubscribers();
                });

                selectAllSubscribersCheckbox?.addEventListener('change', function() {
                    subscriberCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateSelectedSubscribers();
                });

                // Individual checkbox change
                subscriberCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', updateSelectedSubscribers);
                });

                // Mass email for subscribers
                massEmailSubscribersBtn?.addEventListener('click', function() {
                    if (selectedSubscribers.length === 0) return;
                    openMassEmailModal(selectedSubscribers, 'newsletter subscribers');
                });

                function updateSelectedSubscribers() {
                    selectedSubscribers = [];
                    subscriberCheckboxes.forEach(checkbox => {
                        if (checkbox.checked) {
                            selectedSubscribers.push({
                                email: checkbox.dataset.subscriberEmail,
                                name: checkbox.dataset.subscriberEmail.split('@')[0] // Use email prefix as name
                            });
                        }
                    });

                    const count = selectedSubscribers.length;
                    subscribersSelectedCount.textContent = count + ' subscriber' + (count !== 1 ? 's' : '') + ' selected';
                    massEmailSubscribersBtn.disabled = count === 0;

                    // Update select all checkbox state
                    selectAllSubscribersCheckbox.checked = count === subscriberCheckboxes.length;
                    selectAllSubscribersCheckbox.indeterminate = count > 0 && count < subscriberCheckboxes.length;
                }

                updateSelectedSubscribers();
            }

            // Enhanced mass email modal function
            function openMassEmailModal(recipients, recipientType) {
                selectedUsers = recipients; // Use the global variable for compatibility

                const recipientsDisplay = document.getElementById('recipientsDisplay');
                recipientsDisplay.innerHTML = recipients.map(recipient =>
                    '<span class="recipient-tag">' + (recipient.name || recipient.email) + ' (' + recipient.email + ')</span>'
                ).join('');

                // Update modal title
                const modalTitle = document.querySelector('.modal-title');
                modalTitle.innerHTML = '<i class="fas fa-envelope"></i> Send Mass Email to ' + recipientType;

                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }

            // Store original table data
            let originalTableData = null;
            let currentView = 'all'; // 'all', 'subscribers'
            let allUsersData = [];

            // Function to update button states
            function updateButtonStates(activeView) {
                const allUsersBtn = document.getElementById('allUsersBtn');
                const subscribedUsersBtn = document.getElementById('subscribedUsersBtn');

                // Remove active class from all buttons
                [allUsersBtn, subscribedUsersBtn].forEach(btn => {
                    if (btn) btn.classList.remove('active');
                });

                // Add active class to current view button
                if (activeView === 'all' && allUsersBtn) {
                    allUsersBtn.classList.add('active');
                } else if (activeView === 'subscribers' && subscribedUsersBtn) {
                    subscribedUsersBtn.classList.add('active');
                }
            }

            // Function to update table content
            function updateTableContent(users, viewType, title) {
                const tableContainer = document.querySelector('.table-container');
                const sectionTitle = document.querySelector('.section h2');
                const selectAllContainer = document.querySelector('.select-all-container');

                // Update section title
                let iconClass = 'users';
                if (viewType === 'subscribers') iconClass = 'envelope-open';

                sectionTitle.innerHTML = '<i class="fas fa-' + iconClass + '"></i> ' + title + ' (' + users.length + ')';

                // Update select all label
                const selectAllLabel = selectAllContainer.querySelector('label');
                let labelText = 'Users';
                if (viewType === 'subscribers') labelText = 'Subscribers';

                selectAllLabel.textContent = 'Select All ' + labelText;

                if (users.length === 0) {
                    let emptyMessage = 'users found';
                    if (viewType === 'subscribers') emptyMessage = 'newsletter subscribers found';

                    tableContainer.innerHTML = '<p style="text-align: center; color: #666; font-style: italic; padding: 20px;">No ' + emptyMessage + '.</p>';
                    return;
                }

                let tableHTML = '';
                if (viewType === 'subscribers') {
                    tableHTML = '<table><thead><tr>' +
                        '<th><input type="checkbox" id="selectAll" style="margin: 0;"></th>' +
                        '<th>ID</th><th>Email</th><th>Subscribed Date</th><th>Source</th><th>Status</th>' +
                        '</tr></thead><tbody>';

                    users.forEach(subscriber => {
                        tableHTML += '<tr>' +
                            '<td><input type="checkbox" class="user-checkbox" data-user-email="' + subscriber.email + '" data-user-name="' + subscriber.email.split('@')[0] + '" style="margin: 0;"></td>' +
                            '<td>' + subscriber.id + '</td>' +
                            '<td>' + subscriber.email + '</td>' +
                            '<td>' + new Date(subscriber.subscribed_at).toLocaleString() + '</td>' +
                            '<td>' + (subscriber.source || 'website') + '</td>' +
                            '<td><span class="status active">Active</span></td>' +
                            '</tr>';
                    });

                    tableHTML += '</tbody></table>';
                } else {
                    // All users view
                    tableHTML = '<table><thead><tr>' +
                        '<th><input type="checkbox" id="selectAll" style="margin: 0;"></th>' +
                        '<th>ID</th><th>Name</th><th>Email</th><th>Registered</th><th>Last Login</th><th>Status</th><th>Verified</th>' +
                        '</tr></thead><tbody>';

                    users.forEach(user => {
                        tableHTML += '<tr>' +
                            '<td><input type="checkbox" class="user-checkbox" data-user-id="' + user.id + '" data-user-email="' + user.email + '" data-user-name="' + user.name + '" style="margin: 0;"></td>' +
                            '<td>' + user.id + '</td>' +
                            '<td>' + user.name + '</td>' +
                            '<td>' + user.email + '</td>' +
                            '<td>' + new Date(user.registration_date).toLocaleDateString() + '</td>' +
                            '<td>' + (user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never') + '</td>' +
                            '<td><span class="status ' + (user.is_active ? 'active' : 'inactive') + '">' + (user.is_active ? 'Active' : 'Inactive') + '</span></td>' +
                            '<td><span class="status ' + (user.email_verified ? 'verified' : 'unverified') + '">' + (user.email_verified ? '✓' : '✗') + '</span></td>' +
                            '</tr>';
                    });

                    tableHTML += '</tbody></table>';
                }

                tableContainer.innerHTML = tableHTML;

                // Re-initialize event listeners
                initializeTableEventListeners();
                currentView = viewType;

                // Update button states
                updateButtonStates(viewType);
            }

            // Function to initialize table event listeners
            function initializeTableEventListeners() {
                const selectAllCheckbox = document.getElementById('selectAll');
                const userCheckboxes = document.querySelectorAll('.user-checkbox');

                // Clear previous selections
                selectedUsers = [];
                updateSelectedCount();

                if (selectAllCheckbox) {
                    selectAllCheckbox.addEventListener('change', function() {
                        const isChecked = this.checked;
                        userCheckboxes.forEach(checkbox => {
                            checkbox.checked = isChecked;
                        });
                        updateSelectedUsers();
                    });
                }

                userCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', updateSelectedUsers);
                });
            }

            // Store original users data on page load
            const originalUsers = ${JSON.stringify(users)};
            allUsersData = originalUsers;

            // All Users button functionality
            const allUsersBtn = document.getElementById('allUsersBtn');
            allUsersBtn?.addEventListener('click', function() {
                updateTableContent(allUsersData, 'all', 'All Registered Users');
            });

            // Subscribed Users button functionality
            const subscribedUsersBtn = document.getElementById('subscribedUsersBtn');
            subscribedUsersBtn?.addEventListener('click', async function() {
                try {
                    const response = await fetch('/api/admin/newsletter-subscribers');
                    const data = await response.json();

                    if (data.success) {
                        updateTableContent(data.subscribers, 'subscribers', 'Newsletter Subscribers');
                    } else {
                        showMessage('Failed to fetch newsletter subscribers.', 'error');
                    }
                } catch (error) {
                    console.error('Error fetching newsletter subscribers:', error);
                    showMessage('Failed to fetch newsletter subscribers.', 'error');
                }
            });
        </script>
    </body>
    </html>
    `;
}

// Serve HTML pages
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'Html pages', 'index.html'));
});

// Serve reset password page
app.get('/reset-password', (req, res) => {
    res.sendFile(path.join(__dirname, 'Html pages', 'reset-password.html'));
});

// API endpoint to get logged-in users
app.get('/api/admin/logged-in-users', async (req, res) => {
    try {
        const loggedInUsers = await new Promise((resolve, reject) => {
            db.all(`
                SELECT DISTINCT u.id, u.name, u.email, u.last_login, s.created_at as session_start
                FROM users u
                INNER JOIN user_sessions s ON u.id = s.user_id
                WHERE s.expires_at > datetime('now')
                ORDER BY s.created_at DESC
            `, [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        res.json({ success: true, users: loggedInUsers });
    } catch (error) {
        console.error('Error fetching logged-in users:', error);
        res.status(500).json({ error: 'Failed to fetch logged-in users' });
    }
});

// API endpoint to get newsletter subscribers
app.get('/api/admin/newsletter-subscribers', async (req, res) => {
    try {
        const subscribers = await new Promise((resolve, reject) => {
            db.all(`
                SELECT id, email, subscribed_at, is_active, source
                FROM newsletter_subscribers
                WHERE is_active = 1
                ORDER BY subscribed_at DESC
            `, [], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        res.json({ success: true, subscribers: subscribers });
    } catch (error) {
        console.error('Error fetching newsletter subscribers:', error);
        res.status(500).json({ error: 'Failed to fetch newsletter subscribers' });
    }
});

// Mass email endpoint
app.post('/api/admin/send-mass-email', async (req, res) => {
    try {
        const { recipients, subject, message } = req.body;

        if (!recipients || !Array.isArray(recipients) || recipients.length === 0) {
            return res.status(400).json({ error: 'No recipients provided' });
        }

        if (!subject || !subject.trim()) {
            return res.status(400).json({ error: 'Subject is required' });
        }

        if (!message || !message.trim()) {
            return res.status(400).json({ error: 'Message is required' });
        }

        const results = {
            successful: [],
            failed: []
        };

        // Send mass email to all recipients at once
        try {
            const emailResult = await emailService.sendMassEmail(recipients, subject.trim(), message.trim());

            if (emailResult.success) {
                // Add successful emails to results
                emailResult.details.forEach(detail => {
                    if (detail.success) {
                        results.successful.push(detail.email);
                    } else {
                        results.failed.push({ email: detail.email, error: detail.error || 'Unknown error' });
                    }
                });

                console.log(`✅ Mass email campaign completed: ${emailResult.totalSent} sent, ${emailResult.totalFailed} failed`);
            } else {
                // If the entire operation failed, mark all as failed
                recipients.forEach(recipient => {
                    results.failed.push({ email: recipient.email, error: emailResult.error || 'Mass email service failed' });
                });
                console.error(`❌ Mass email service failed:`, emailResult.error);
            }
        } catch (error) {
            // If there's an exception, mark all as failed
            recipients.forEach(recipient => {
                results.failed.push({ email: recipient.email, error: error.message });
            });
            console.error(`❌ Mass email exception:`, error);
        }

        console.log(`📧 Mass email campaign completed: ${results.successful.length} successful, ${results.failed.length} failed`);

        res.json({
            success: true,
            message: `Mass email sent successfully to ${results.successful.length} recipient(s)`,
            results: results
        });

    } catch (error) {
        console.error('Mass email error:', error);
        res.status(500).json({ error: 'Failed to send mass email. Please try again later.' });
    }
});

// Newsletter subscription endpoint
app.post('/api/newsletter/subscribe', async (req, res) => {
    try {
        const { email } = req.body;

        // Validate email
        if (!email || !validateEmail(email)) {
            return res.status(400).json({ error: 'Please provide a valid email address' });
        }

        // Check if email is already subscribed
        const existingSubscriber = await new Promise((resolve, reject) => {
            db.get('SELECT id, is_active FROM newsletter_subscribers WHERE email = ?', [email.toLowerCase()], (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });

        if (existingSubscriber) {
            if (existingSubscriber.is_active) {
                return res.status(400).json({ error: 'This email is already subscribed to our newsletter' });
            } else {
                // Reactivate subscription
                await new Promise((resolve, reject) => {
                    db.run(
                        'UPDATE newsletter_subscribers SET is_active = 1, updated_at = CURRENT_TIMESTAMP WHERE email = ?',
                        [email.toLowerCase()],
                        function (err) {
                            if (err) reject(err);
                            else resolve();
                        }
                    );
                });

                console.log(`📧 Reactivated newsletter subscriber: ${email}`);
            }
        } else {
            // Add new subscriber
            await new Promise((resolve, reject) => {
                db.run(
                    'INSERT INTO newsletter_subscribers (email) VALUES (?)',
                    [email.toLowerCase()],
                    function (err) {
                        if (err) reject(err);
                        else resolve({ id: this.lastID });
                    }
                );
            });

            console.log(`📧 New newsletter subscriber: ${email}`);
        }

        // Send welcome email to subscriber (don't wait for it to complete)
        emailService.sendNewsletterWelcomeEmail(email.toLowerCase())
            .then(emailResult => {
                if (emailResult.success) {
                    console.log(`✅ Newsletter welcome email sent to ${email}`);
                } else {
                    console.error(`❌ Failed to send newsletter welcome email to ${email}:`, emailResult.error);
                }
            })
            .catch(emailError => {
                console.error(`❌ Newsletter welcome email error for ${email}:`, emailError);
            });

        // Send notification email to site owner (don't wait for it to complete)
        emailService.sendNewsletterNotificationEmail(email.toLowerCase())
            .then(notificationResult => {
                if (notificationResult.success) {
                    console.log(`✅ Newsletter subscription notification sent to owner`);
                } else {
                    console.error(`❌ Failed to send newsletter notification to owner:`, notificationResult.error);
                }
            })
            .catch(notificationError => {
                console.error(`❌ Newsletter notification error:`, notificationError);
            });

        res.json({
            success: true,
            message: 'Successfully subscribed to newsletter! Check your email for a welcome message.'
        });

    } catch (error) {
        console.error('Newsletter subscription error:', error);
        res.status(500).json({ error: 'Failed to subscribe to newsletter. Please try again later.' });
    }
});

// Test image upload and email endpoint
app.post('/api/test-send-image', upload.single('image'), async (req, res) => {
    try {
        // Check if file was uploaded
        if (!req.file) {
            return res.status(400).json({
                success: false,
                error: 'No image file uploaded'
            });
        }

        const { fileName } = req.body;
        const imageBuffer = req.file.buffer;
        const originalFileName = fileName || req.file.originalname;

        console.log(`📤 Processing test image upload: ${originalFileName}`);
        console.log(`📊 File size: ${(req.file.size / 1024 / 1024).toFixed(2)} MB`);
        console.log(`📋 File type: ${req.file.mimetype}`);

        // Send email with image attachment
        const emailResult = await emailService.sendTestImageEmail(imageBuffer, originalFileName);

        if (emailResult.success) {
            console.log('✅ Test image email sent successfully');
            res.json({
                success: true,
                message: 'Image sent <NAME_EMAIL>',
                messageId: emailResult.messageId,
                previewURL: emailResult.previewURL
            });
        } else {
            console.error('❌ Failed to send test image email:', emailResult.error);
            res.status(500).json({
                success: false,
                error: emailResult.error || 'Failed to send email'
            });
        }

    } catch (error) {
        console.error('❌ Test image upload error:', error);

        // Handle multer errors
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                error: 'File size too large. Maximum size is 10MB.'
            });
        }

        if (error.message === 'Only image files are allowed!') {
            return res.status(400).json({
                success: false,
                error: 'Only image files are allowed.'
            });
        }

        res.status(500).json({
            success: false,
            error: 'Failed to process image upload. Please try again.'
        });
    }
});

// ===== ORDERS API ENDPOINTS =====

// Get user orders
app.get('/api/orders', authenticateToken, async (req, res) => {
    try {
        const { page = 1, limit = 10, status } = req.query;
        const offset = (page - 1) * limit;

        // Build query with optional status filter
        let query = `
            SELECT o.*, 
                   COUNT(oi.id) as item_count,
                   GROUP_CONCAT(oi.product_name, ', ') as product_names
            FROM orders o
            LEFT JOIN order_items oi ON o.id = oi.order_id
            WHERE o.user_id = ?
        `;
        let params = [req.user.userId];

        if (status) {
            query += ' AND o.status = ?';
            params.push(status);
        }

        query += `
            GROUP BY o.id
            ORDER BY o.created_at DESC
            LIMIT ? OFFSET ?
        `;
        params.push(parseInt(limit), parseInt(offset));

        const orders = await new Promise((resolve, reject) => {
            db.all(query, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        // Get total count for pagination
        let countQuery = 'SELECT COUNT(*) as total FROM orders WHERE user_id = ?';
        let countParams = [req.user.userId];

        if (status) {
            countQuery += ' AND status = ?';
            countParams.push(status);
        }

        const totalCount = await new Promise((resolve, reject) => {
            db.get(countQuery, countParams, (err, row) => {
                if (err) reject(err);
                else resolve(row.total);
            });
        });

        // Format orders data
        const formattedOrders = orders.map(order => ({
            id: order.id,
            orderNumber: order.order_number,
            status: order.status,
            paymentStatus: order.payment_status,
            orderDate: order.order_date || order.created_at,
            subtotal: parseFloat(order.subtotal || 0),
            shipping: parseFloat(order.shipping || 0),
            tax: parseFloat(order.tax || 0),
            total: parseFloat(order.total_amount),
            itemCount: order.item_count,
            productNames: order.product_names,
            trackingNumber: order.tracking_number,
            estimatedDelivery: order.estimated_delivery,
            shippingAddress: order.shipping_address ? JSON.parse(order.shipping_address) : null
        }));

        res.json({
            success: true,
            orders: formattedOrders,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: totalCount,
                pages: Math.ceil(totalCount / limit)
            }
        });

    } catch (error) {
        console.error('Get orders error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch orders'
        });
    }
});

// Get specific order details
app.get('/api/orders/:orderId', authenticateToken, async (req, res) => {
    try {
        const { orderId } = req.params;

        // Get order details
        const order = await new Promise((resolve, reject) => {
            db.get(
                'SELECT * FROM orders WHERE id = ? AND user_id = ?',
                [orderId, req.user.userId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (!order) {
            return res.status(404).json({
                success: false,
                error: 'Order not found'
            });
        }

        // Get order items
        const orderItems = await new Promise((resolve, reject) => {
            db.all(
                'SELECT * FROM order_items WHERE order_id = ?',
                [orderId],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                }
            );
        });

        // Format order data
        const formattedOrder = {
            id: order.id,
            orderNumber: order.order_number,
            status: order.status,
            paymentStatus: order.payment_status,
            paymentMethod: order.payment_method,
            orderDate: order.order_date || order.created_at,
            subtotal: parseFloat(order.subtotal || 0),
            shipping: parseFloat(order.shipping || 0),
            tax: parseFloat(order.tax || 0),
            total: parseFloat(order.total_amount),
            trackingNumber: order.tracking_number,
            estimatedDelivery: order.estimated_delivery,
            shippingAddress: order.shipping_address ? JSON.parse(order.shipping_address) : null,
            billingAddress: order.billing_address ? JSON.parse(order.billing_address) : null,
            items: orderItems.map(item => ({
                id: item.id,
                productId: item.product_id,
                productName: item.product_name,
                productType: item.product_type,
                productImage: item.product_image,
                quantity: item.quantity,
                unitPrice: parseFloat(item.unit_price),
                totalPrice: parseFloat(item.total_price),
                customization: item.customization_data ? JSON.parse(item.customization_data) : null
            }))
        };

        res.json({
            success: true,
            order: formattedOrder
        });

    } catch (error) {
        console.error('Get order details error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch order details'
        });
    }
});

// Cancel order
app.put('/api/orders/:orderId/cancel', authenticateToken, async (req, res) => {
    try {
        const { orderId } = req.params;

        // Get order to check if it can be cancelled
        const order = await new Promise((resolve, reject) => {
            db.get(
                'SELECT * FROM orders WHERE id = ? AND user_id = ?',
                [orderId, req.user.userId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (!order) {
            return res.status(404).json({
                success: false,
                error: 'Order not found'
            });
        }

        // Check if order can be cancelled (only pending orders)
        if (order.status !== 'pending') {
            return res.status(400).json({
                success: false,
                error: 'Order cannot be cancelled at this stage'
            });
        }

        // Update order status to cancelled
        await new Promise((resolve, reject) => {
            db.run(
                'UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                ['cancelled', orderId],
                function (err) {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });

        res.json({
            success: true,
            message: 'Order cancelled successfully'
        });

    } catch (error) {
        console.error('Cancel order error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to cancel order'
        });
    }
});

// Reorder items from previous order
app.post('/api/orders/:orderId/reorder', authenticateToken, async (req, res) => {
    try {
        const { orderId } = req.params;

        // Get order to verify ownership
        const order = await new Promise((resolve, reject) => {
            db.get(
                'SELECT * FROM orders WHERE id = ? AND user_id = ?',
                [orderId, req.user.userId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (!order) {
            return res.status(404).json({
                success: false,
                error: 'Order not found'
            });
        }

        // Get order items
        const orderItems = await new Promise((resolve, reject) => {
            db.all(
                'SELECT * FROM order_items WHERE order_id = ?',
                [orderId],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                }
            );
        });

        // Format items for cart
        const cartItems = orderItems.map(item => ({
            productId: item.product_id,
            productName: item.product_name,
            productType: item.product_type,
            productImage: item.product_image,
            quantity: item.quantity,
            price: parseFloat(item.unit_price),
            customization: item.customization_data ? JSON.parse(item.customization_data) : null
        }));

        res.json({
            success: true,
            message: 'Items ready to be added to cart',
            cartItems: cartItems
        });

    } catch (error) {
        console.error('Reorder error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to process reorder'
        });
    }
});

// Download invoice (placeholder - would generate PDF in real implementation)
app.get('/api/orders/:orderId/invoice', authenticateToken, async (req, res) => {
    try {
        const { orderId } = req.params;

        // Get order to verify ownership and completion
        const order = await new Promise((resolve, reject) => {
            db.get(
                'SELECT * FROM orders WHERE id = ? AND user_id = ?',
                [orderId, req.user.userId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (!order) {
            return res.status(404).json({
                success: false,
                error: 'Order not found'
            });
        }

        // Only allow invoice download for completed orders
        if (order.status !== 'delivered' && order.status !== 'completed') {
            return res.status(400).json({
                success: false,
                error: 'Invoice not available for this order status'
            });
        }

        // For now, return invoice data as JSON
        // In a real implementation, this would generate and return a PDF
        const orderItems = await new Promise((resolve, reject) => {
            db.all(
                'SELECT * FROM order_items WHERE order_id = ?',
                [orderId],
                (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                }
            );
        });

        const invoiceData = {
            orderNumber: order.order_number,
            orderDate: order.order_date || order.created_at,
            status: order.status,
            subtotal: parseFloat(order.subtotal || 0),
            shipping: parseFloat(order.shipping || 0),
            tax: parseFloat(order.tax || 0),
            total: parseFloat(order.total_amount),
            shippingAddress: order.shipping_address ? JSON.parse(order.shipping_address) : null,
            items: orderItems.map(item => ({
                productName: item.product_name,
                quantity: item.quantity,
                unitPrice: parseFloat(item.unit_price),
                totalPrice: parseFloat(item.total_price)
            }))
        };

        res.json({
            success: true,
            message: 'Invoice data retrieved (PDF generation would be implemented here)',
            invoice: invoiceData
        });

    } catch (error) {
        console.error('Download invoice error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to generate invoice'
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Endpoint not found' });
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down gracefully...');
    db.close((err) => {
        if (err) {
            console.error('Error closing database:', err.message);
        } else {
            console.log('Database connection closed.');
        }
        process.exit(0);
    });
});

// Clean URL routes for Stripe redirects
app.get('/payment-success', (req, res) => {
    res.sendFile(path.join(__dirname, 'Html pages', 'payment-success.html'));
});

app.get('/cart', (req, res) => {
    res.sendFile(path.join(__dirname, 'Html pages', 'cart.html'));
});


// Start server
app.listen(PORT, async () => {
    console.log(`🚀 Server running on http://localhost:${PORT}`);
    console.log(`📁 Database: ./database/users.db`);
    console.log(`🔒 JWT Secret: ${JWT_SECRET.substring(0, 10)}...`);

    // Test email configuration (don't let this crash the server)
    try {
        console.log('\n📧 Testing email configuration...');
        const emailConfigValid = await emailService.testEmailConfiguration();
        if (emailConfigValid) {
            console.log('✅ Email service is ready to send welcome emails');
        } else {
            console.log('⚠️  Email service configuration issue - welcome emails may not work');
            console.log('   Check your .env file for email settings');
        }
    } catch (emailError) {
        console.error('❌ Email service error during startup:', emailError.message);
        console.log('⚠️  Continuing without email service - registration will still work');
    }

    // Payment system status
    console.log('\n💳 Payment System Status:');
    if (isLiveMode) {
        console.log('🟢 LIVE PAYMENTS ENABLED - Real money will be processed!');
        console.log('💰 Payments will go to your connected bank account');
        console.log('⚠️  Make sure your Stripe account is fully verified');
    } else if (isTestMode) {
        console.log('🟡 TEST MODE - Use test cards only (no real money)');
        console.log('📝 Test cards: ****************, ****************, etc.');
        console.log('🔄 Switch to live keys in .env for real payments');
    } else {
        console.log('🔴 PAYMENT SYSTEM NOT CONFIGURED');
        console.log('🔧 Add your Stripe keys to .env file to enable payments');
        console.log('📖 See .env file for setup instructions');
    }
    console.log('');
});

module.exports = app;
