/* Admin Dashboard Styles */

/* Admin Body Layout */
.admin-body {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
    min-height: 100vh;
    color: #ffffff;
    font-family: var(--font-family);
}

/* Admin Header */
.admin-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.admin-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-logo h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-user span {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

/* Admin Main Content */
.admin-main {
    padding: 2rem 0;
}

.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Dashboard Stats */
.admin-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.stat-icon {
    font-size: 3rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 50%;
    flex-shrink: 0;
}

.stat-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffffff;
}

.stat-content p {
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
}

/* Admin Sections */
.admin-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    color: #ffffff;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: #ffffff;
    font-size: 0.9rem;
    min-width: 200px;
    transition: var(--transition);
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

/* Admin Table */
.table-container {
    overflow-x: auto;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(255, 255, 255, 0.02);
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-table th {
    background: rgba(255, 255, 255, 0.05);
    font-weight: 600;
    color: #ffffff;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.admin-table td {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
}

.admin-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: rgba(46, 213, 115, 0.2);
    color: #2ed573;
    border: 1px solid #2ed573;
}

.status-badge.inactive {
    background: rgba(255, 71, 87, 0.2);
    color: #ff4757;
    border: 1px solid #ff4757;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    border-radius: 6px;
}

/* Table Pagination */
.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination-info {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-numbers {
    display: flex;
    gap: 0.5rem;
}

.page-number {
    padding: 0.5rem 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    font-size: 0.9rem;
}

.page-number:hover,
.page-number.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: #ffffff;
}

/* Activity List */
.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.activity-icon.login {
    background: rgba(46, 213, 115, 0.2);
    color: #2ed573;
}

.activity-icon.signup {
    background: rgba(58, 123, 213, 0.2);
    color: #3a7bd5;
}

.activity-icon.booking {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #ffffff;
}

.activity-content p {
    margin: 0;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.activity-time {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.5);
    flex-shrink: 0;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 10000;
}

.loading-spinner-large {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-overlay p {
    color: #ffffff;
    font-size: 1.1rem;
    margin: 0;
}

/* User Detail Grid */
.user-detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin: 1rem 0;
}

.user-detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.user-detail-item label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-detail-item span {
    color: #ffffff;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .admin-container {
        padding: 0 1rem;
    }

    .admin-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .admin-header-content {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .admin-user {
        justify-content: center;
    }

    .admin-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .section-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .search-input {
        min-width: 100%;
    }

    .table-container {
        font-size: 0.8rem;
    }

    .admin-table th,
    .admin-table td {
        padding: 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .table-pagination {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .user-detail-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 5% auto;
        width: 95%;
    }
}

@media (max-width: 480px) {
    .admin-section {
        padding: 1rem;
        border-radius: 12px;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }

    .stat-content h3 {
        font-size: 2rem;
    }
}
