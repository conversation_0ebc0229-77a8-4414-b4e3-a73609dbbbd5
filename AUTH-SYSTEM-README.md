# 🔐 New Authentication System

A complete authentication and user management system built from scratch with modern security practices.

## 🚀 Features

### Authentication
- ✅ User registration with email verification
- ✅ Secure login with JWT tokens
- ✅ Password reset functionality
- ✅ Session management with device tracking
- ✅ Account lockout after failed attempts
- ✅ Audit logging for security events

### User Management
- ✅ User profile management
- ✅ Address management (multiple addresses per user)
- ✅ Notification preferences
- ✅ Password change functionality
- ✅ Account deactivation/deletion
- ✅ User activity tracking
- ✅ User statistics

### Security Features
- ✅ bcrypt password hashing (12 salt rounds)
- ✅ JWT token authentication
- ✅ Rate limiting on authentication endpoints
- ✅ Account lockout (5 failed attempts, 15-minute lockout)
- ✅ Input validation and sanitization
- ✅ CORS protection
- ✅ Helmet security headers
- ✅ SQL injection protection
- ✅ Comprehensive audit logging

## 📁 File Structure

```
├── database/
│   ├── schema.sql              # Database schema with 8 tables
│   ├── database.js             # Database manager with utilities
│   └── auth_system.db          # SQLite database (created automatically)
├── services/
│   ├── authService.js          # Authentication service
│   └── userService.js          # User management service
├── public/
│   └── js/
│       └── new-auth.js         # Frontend integration
├── auth-server.js              # Main server file
├── test-auth-system.js         # Comprehensive test suite
├── package-auth.json           # Dependencies
├── .env.example                # Environment configuration
└── AUTH-SYSTEM-README.md       # This file
```

## 🛠️ Installation

1. **Install Dependencies**
   ```bash
   # Copy package-auth.json to package.json
   cp package-auth.json package.json
   
   # Install dependencies
   npm install
   ```

2. **Environment Setup**
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit .env with your configuration
   # At minimum, change JWT_SECRET to a secure random string
   ```

3. **Initialize Database**
   ```bash
   # The database will be created automatically when you start the server
   # Or run the test script to initialize and test everything
   node test-auth-system.js
   ```

## 🚀 Usage

### Start the Server
```bash
# Development mode with auto-restart
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3001` (or your configured PORT).

### Test the System
```bash
# Run comprehensive tests
node test-auth-system.js
```

### Frontend Integration

Include the frontend script in your HTML:
```html
<script src="/js/new-auth.js"></script>
```

The script automatically initializes and provides a global `newAuth` object:

```javascript
// Check if user is logged in
if (newAuth.isAuthenticated()) {
    console.log('User is logged in:', newAuth.user);
}

// Login programmatically
await newAuth.makeRequest('/auth/login', {
    method: 'POST',
    body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password'
    })
});
```

## 📡 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/verify` - Verify JWT token
- `POST /api/auth/logout` - User logout
- `POST /api/auth/verify-email` - Verify email address
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password

### User Management
- `GET /api/user/profile` - Get user profile
- `PUT /api/user/info` - Update user basic info
- `PUT /api/user/profile` - Update user profile
- `PUT /api/user/preferences` - Update notification preferences
- `PUT /api/user/password` - Change password
- `POST /api/user/addresses` - Add address
- `PUT /api/user/addresses/:id` - Update address
- `DELETE /api/user/addresses/:id` - Delete address
- `GET /api/user/activity` - Get activity log
- `GET /api/user/stats` - Get user statistics
- `GET /api/user/sessions` - Get user sessions
- `DELETE /api/user/sessions/:id` - Revoke session
- `DELETE /api/user/sessions` - Revoke all sessions
- `PUT /api/user/deactivate` - Deactivate account
- `DELETE /api/user/account` - Delete account permanently

### Admin (Optional)
- `GET /api/admin/stats` - Database statistics
- `POST /api/admin/cleanup` - Database cleanup

## 🔒 Security Configuration

### JWT Configuration
```env
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=30d
```

### Rate Limiting
```env
RATE_LIMIT_WINDOW=15          # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100   # 100 requests per window
AUTH_RATE_LIMIT_MAX=10        # 10 auth attempts per window
```

### Account Security
```env
MAX_LOGIN_ATTEMPTS=5          # Failed attempts before lockout
ACCOUNT_LOCKOUT_DURATION=15   # Lockout duration in minutes
BCRYPT_SALT_ROUNDS=12         # bcrypt salt rounds
```

## 🧪 Testing

The system includes a comprehensive test suite that verifies:

- Database initialization
- User registration and email verification
- Login and token verification
- Profile and address management
- Password changes and resets
- Session management
- Security features
- Database cleanup

Run tests with:
```bash
node test-auth-system.js
```

## 🔧 Development

### Database Schema
The system uses 8 tables:
- `users` - User accounts
- `user_sessions` - Active sessions
- `email_verifications` - Email verification tokens
- `password_resets` - Password reset tokens
- `login_attempts` - Failed login tracking
- `user_profiles` - Extended user information
- `user_addresses` - User addresses
- `audit_log` - Security audit trail

### Adding New Features
1. Add database schema changes to `database/schema.sql`
2. Implement service methods in `services/`
3. Add API endpoints in `auth-server.js`
4. Update frontend integration in `public/js/new-auth.js`
5. Add tests to `test-auth-system.js`

## 🚨 Important Notes

1. **Change JWT_SECRET** in production to a secure random string
2. **Enable email service** for production email verification and password resets
3. **Configure CORS** origins for your production domain
4. **Set up HTTPS** in production
5. **Regular database backups** are recommended
6. **Monitor audit logs** for security events

## 📝 Example Usage

See the test file `test-auth-system.js` for comprehensive examples of how to use all features.

## 🆘 Support

If you encounter any issues:
1. Check the console logs for error messages
2. Verify your `.env` configuration
3. Run the test suite to identify problems
4. Check database permissions and file paths

---

**Built with security and scalability in mind. Ready for production use with proper configuration.**
