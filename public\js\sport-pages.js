// Sport Pages JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize sport page
    initializeSportPage();
    
    // Set minimum date to today for all date inputs
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        const today = new Date().toISOString().split('T')[0];
        input.min = today;
        if (!input.value) {
            input.value = today;
        }
    });

    // Handle sport-specific booking forms
    const footballForm = document.getElementById('football-booking-form');
    const tennisForm = document.getElementById('tennis-booking-form');
    const padelForm = document.getElementById('padel-booking-form');

    if (footballForm) {
        footballForm.addEventListener('submit', handleFootballBooking);
    }
    
    if (tennisForm) {
        tennisForm.addEventListener('submit', handleTennisBooking);
    }
    
    if (padelForm) {
        padelForm.addEventListener('submit', handlePadelBooking);
    }

    // Package booking buttons
    const packageButtons = document.querySelectorAll('.btn-package');
    packageButtons.forEach(button => {
        button.addEventListener('click', handlePackageBooking);
    });

    // Service booking buttons
    const serviceButtons = document.querySelectorAll('.service-card .btn-secondary');
    serviceButtons.forEach(button => {
        button.addEventListener('click', handleServiceBooking);
    });
});

function initializeSportPage() {
    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe cards for animation
    const animateElements = document.querySelectorAll(
        '.pitch-type-card, .court-type-card, .venue-card, .service-card, .package-card, .rule-card, .tip-card'
    );
    
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // Add hover effects to venue cards
    const venueCards = document.querySelectorAll('.venue-card');
    venueCards.forEach(card => {
        card.addEventListener('click', function(e) {
            if (!e.target.closest('.btn-venue')) {
                const venueLink = this.querySelector('.btn-venue');
                if (venueLink) {
                    venueLink.click();
                }
            }
        });
    });
}

// Find pitches/courts functions
function findPitches(pitchType) {
    const searchParams = new URLSearchParams({
        sport: 'football',
        pitchType: pitchType,
        date: new Date().toISOString().split('T')[0]
    });
    
    window.location.href = `search-results.html?${searchParams.toString()}`;
}

function findCourts(courtType) {
    const searchParams = new URLSearchParams({
        sport: 'tennis',
        courtType: courtType,
        date: new Date().toISOString().split('T')[0]
    });
    
    window.location.href = `search-results.html?${searchParams.toString()}`;
}

// Form submission handlers
function handleFootballBooking(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const bookingData = {
        sport: 'football',
        pitchType: formData.get('pitchType'),
        location: formData.get('location'),
        date: formData.get('date'),
        time: formData.get('time')
    };
    
    if (!bookingData.pitchType || !bookingData.date) {
        showNotification('Please select a pitch type and date', 'error');
        return;
    }
    
    processBookingSearch(bookingData, e.target);
}

function handleTennisBooking(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const bookingData = {
        sport: 'tennis',
        courtType: formData.get('courtType'),
        location: formData.get('location'),
        date: formData.get('date'),
        time: formData.get('time')
    };
    
    if (!bookingData.courtType || !bookingData.date) {
        showNotification('Please select a court type and date', 'error');
        return;
    }
    
    processBookingSearch(bookingData, e.target);
}

function handlePadelBooking(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const bookingData = {
        sport: 'padel',
        packageType: formData.get('packageType'),
        location: formData.get('location'),
        date: formData.get('date'),
        players: formData.get('players')
    };
    
    if (!bookingData.packageType || !bookingData.date || !bookingData.players) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }
    
    processBookingSearch(bookingData, e.target);
}

function processBookingSearch(bookingData, form) {
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Searching...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Reset button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
        
        // Navigate to results page
        const searchParams = new URLSearchParams(bookingData);
        window.location.href = `search-results.html?${searchParams.toString()}`;
    }, 1500);
}

function handlePackageBooking(e) {
    const packageCard = e.target.closest('.package-card');
    const packageTitle = packageCard.querySelector('.package-title').textContent;
    const packagePrice = packageCard.querySelector('.package-price').textContent;
    
    // Store package info for booking process
    sessionStorage.setItem('selectedPackage', JSON.stringify({
        title: packageTitle,
        price: packagePrice,
        type: 'padel-package'
    }));
    
    // Navigate to booking page with package pre-selected
    const searchParams = new URLSearchParams({
        sport: 'padel',
        package: packageTitle.toLowerCase().replace(' ', '-'),
        date: new Date().toISOString().split('T')[0]
    });
    
    window.location.href = `search-results.html?${searchParams.toString()}`;
}

function handleServiceBooking(e) {
    const serviceCard = e.target.closest('.service-card');
    const serviceTitle = serviceCard.querySelector('.service-title').textContent;
    
    // Handle different service types
    switch (serviceTitle) {
        case 'Professional Coaching':
            window.location.href = 'coaching-booking.html';
            break;
        case 'Equipment Rental':
            showNotification('Equipment rental is available during court booking', 'info');
            break;
        case 'Tournament Hosting':
            window.location.href = 'contact.html?subject=Tournament%20Hosting';
            break;
        default:
            showNotification('Service booking coming soon!', 'info');
    }
}

// Venue filtering functions
function filterVenuesBySport(sport) {
    const venueCards = document.querySelectorAll('.venue-card');
    
    venueCards.forEach(card => {
        const sportBadges = card.querySelectorAll('.sport-badge');
        let hasMatchingSport = false;
        
        if (!sport) {
            hasMatchingSport = true;
        } else {
            sportBadges.forEach(badge => {
                if (badge.classList.contains(sport) || badge.textContent.toLowerCase().includes(sport)) {
                    hasMatchingSport = true;
                }
            });
        }
        
        if (hasMatchingSport) {
            card.style.display = 'block';
            card.style.animation = 'fadeInUp 0.5s ease';
        } else {
            card.style.display = 'none';
        }
    });
}

function filterVenuesByLocation(location) {
    const venueCards = document.querySelectorAll('.venue-card');
    
    venueCards.forEach(card => {
        const venueLocation = card.querySelector('.venue-location').textContent.toLowerCase();
        
        if (!location || location === '' || venueLocation.includes(location.toLowerCase())) {
            card.style.display = 'block';
            card.style.animation = 'fadeInUp 0.5s ease';
        } else {
            card.style.display = 'none';
        }
    });
}

// Price calculator for dynamic pricing
function calculatePrice(basePrice, timeSlot, date, duration = 1) {
    let price = basePrice * duration;
    
    // Peak time multiplier
    const hour = parseInt(timeSlot.split(':')[0]);
    if (hour >= 18 && hour <= 21) { // Evening peak
        price *= 1.2;
    } else if (hour >= 6 && hour <= 9) { // Morning peak
        price *= 1.1;
    }
    
    // Weekend multiplier
    const dayOfWeek = new Date(date).getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday or Saturday
        price *= 1.3;
    }
    
    return Math.round(price * 100) / 100; // Round to 2 decimal places
}

// Availability checker
async function checkAvailability(facilityId, date, timeSlot) {
    try {
        // This would normally be an API call
        // For now, simulate with random availability
        const isAvailable = Math.random() > 0.3; // 70% chance of availability
        
        return {
            available: isAvailable,
            facilityId: facilityId,
            date: date,
            timeSlot: timeSlot,
            price: calculatePrice(25, timeSlot, date) // Base price of €25
        };
    } catch (error) {
        console.error('Error checking availability:', error);
        return { available: false, error: 'Unable to check availability' };
    }
}

// Booking analytics
function trackBookingInteraction(action, sport, details = {}) {
    // This would normally send to analytics service
    console.log('Booking Analytics:', {
        action: action,
        sport: sport,
        details: details,
        timestamp: new Date().toISOString(),
        page: window.location.pathname
    });
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .venue-card {
        cursor: pointer;
    }
    
    .package-card.featured {
        position: relative;
        overflow: visible;
    }
    
    .package-card.featured::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
        border-radius: var(--border-radius);
        z-index: -1;
    }
    
    .rule-card:nth-child(odd) .rule-number {
        background: var(--primary-color);
    }
    
    .rule-card:nth-child(even) .rule-number {
        background: var(--secondary-color);
    }
    
    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255,255,255,.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        findPitches,
        findCourts,
        handleFootballBooking,
        handleTennisBooking,
        handlePadelBooking,
        calculatePrice,
        checkAvailability,
        trackBookingInteraction
    };
}